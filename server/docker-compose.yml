version: '3.8'

services:
  app:
    build: .
    ports:
      - "8080:8080"
    environment:
      - CLICKHOUSE_URL=*****************************************
      - CLICKHOUSE_USERNAME=default
      - CLICKHOUSE_PASSWORD=clickhouse
    depends_on:
      - clickhouse

  clickhouse:
    image: clickhouse/clickhouse-server:latest
    ports:
      - "8123:8123"
      - "9000:9000"
    environment:
      - CLICKHOUSE_USER=default
      - CLICKHOUSE_PASSWORD=clickhouse
      - CLICKHOUSE_DEFAULT_ACCESS_MANAGEMENT=1
    volumes:
      - clickhouse-data:/var/lib/clickhouse

volumes:
  clickhouse-data: