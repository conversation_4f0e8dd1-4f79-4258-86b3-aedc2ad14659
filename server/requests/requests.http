###
GET http://localhost:8080/ping

###
POST http://localhost:8080/api/query
Content-Type: application/json

{
  "name": "get_transactions",
  "from": 1640995200,
  "to": 1641945600
}

> {%
    client.test("Response data should have at least one row", function () {
        client.assert(response.body.data.rows.length > 0, "Data array should not be empty");
    });
%}

###
POST http://localhost:8080/api/query
Content-Type: application/json

{
  "name": "get_sales_volume",
  "sku" : "Smart Watch Elite",
  "storeId": "store_001",
  "startDate": 1704096000,
  "endDate": 1704201600
}

> {%
    client.test("Response data should have at least one row", function () {
        client.assert(response.body.data.rows.length > 0, "Data array should not be empty");
    });
%}

###
POST http://localhost:8080/api/query
Content-Type: application/json

{
  "name": "get_days_on_hand",
  "sku": "Smart Watch Elite",
  "startDate": 1704096000,
  "endDate": 1704201600
}

> {%
    client.test("Response data should have at least one row", function () {
        client.assert(response.body.data.rows.length > 0, "Data array should not be empty");
    });
%}

###
POST http://localhost:8080/api/query
Content-Type: application/json

{
  "name": "get_daily_inventory_sold",
  "storeId": "store_001",
  "sku": "Smart Watch Elite",
  "startDate": 1704096000,
  "endDate": 1704201600
}

> {%
    client.test("Response data should have at least one row", function () {
        client.assert(response.body.data.rows.length > 0, "Data array should not be empty");
    });
%}

###
POST http://localhost:8080/api/query
Content-Type: application/json

{
  "name": "get_sku_inventory",
  "startDate": 1704096000,
  "endDate": 1704201600
}

> {%
    client.test("Response data should have at least one row", function () {
        client.assert(response.body.data.rows.length > 0, "Data array should not be empty");
    });
%}