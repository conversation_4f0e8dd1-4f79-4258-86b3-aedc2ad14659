package co.datainsider.salesreport.query.controller;

import com.fasterxml.jackson.annotation.JsonSubTypes;
import com.fasterxml.jackson.annotation.JsonTypeInfo;

@JsonTypeInfo(
        use = JsonTypeInfo.Id.NAME,
        include = JsonTypeInfo.As.PROPERTY,
        property = "name"
)
@JsonSubTypes({
        @JsonSubTypes.Type(value = GetTransactions.class, name = QueryRequest.QueryTypes.GET_TRANSACTIONS),
        @JsonSubTypes.Type(value = GetSalesVolume.class, name = QueryRequest.QueryTypes.GET_SALES_VOLUME),
        @JsonSubTypes.Type(value = GetDaysOnHand.class, name = QueryRequest.QueryTypes.GET_DAYS_ON_HAND),
        @JsonSubTypes.Type(value = GetDailyInventorySold.class, name = QueryRequest.QueryTypes.GET_DAILY_INVENTORY_SOLD),
        @JsonSubTypes.Type(value = GetSkuInventory.class, name = QueryRequest.QueryTypes.GET_SKU_INVENTORY)
})
public abstract class QueryRequest {
    public static class QueryTypes {
        public static final String GET_SALES_VOLUME = "get_sales_volume";
        public static final String GET_TRANSACTIONS = "get_transactions";
        public static final String GET_DAYS_ON_HAND = "get_days_on_hand";
        public static final String GET_DAILY_INVENTORY_SOLD = "get_daily_inventory_sold";
        public static final String GET_SKU_INVENTORY = "get_sku_inventory";
    }
    private final String name;

    public QueryRequest(String name) {
        this.name = name;
    }

    public String getName() {
        return name;
    }

    /**
     * Returns query parameters as an array. The order of parameters in the array
     * must match the order of parameters (?) in the corresponding SQL query.
     *
     * @return Array of query parameters
     */
    public abstract Object[] getParams();
}

class GetSalesVolume extends QueryRequest {
    private String storeId;
    private String sku;
    private Long startDate;
    private Long endDate;

    public GetSalesVolume() {
        super(QueryTypes.GET_SALES_VOLUME);
    }

    public GetSalesVolume(String storeId, String sku, Long startDate, Long endDate) {
        super(QueryTypes.GET_SALES_VOLUME);
        this.storeId = storeId;
        this.sku = sku;
        this.startDate = startDate;
        this.endDate = endDate;
    }

    public String getStoreId() {
        return storeId;
    }

    public void setStoreId(String storeId) {
        this.storeId = storeId;
    }

    public String getSku() {
        return sku;
    }

    public void setSku(String sku) {
        this.sku = sku;
    }

    public Long getStartDate() {
        return startDate;
    }

    public void setStartDate(Long startDate) {
        this.startDate = startDate;
    }

    public Long getEndDate() {
        return endDate;
    }

    public void setEndDate(Long endDate) {
        this.endDate = endDate;
    }

    @Override
    public Object[] getParams() {
        return new Object[]{storeId, sku, startDate, endDate};
    }
}
class GetTransactions extends QueryRequest {
    private Long from;
    private Long to;

    public GetTransactions() {
        super(QueryTypes.GET_TRANSACTIONS);
    }

    public GetTransactions(Long from, Long to) {
        super(QueryTypes.GET_TRANSACTIONS);
        this.from = from;
        this.to = to;
    }

    public Long getFrom() {
        return from;
    }

    public void setFrom(Long from) {
        this.from = from;
    }

    public Long getTo() {
        return to;
    }

    public void setTo(Long to) {
        this.to = to;
    }

    @Override
    public Object[] getParams() {
        return new Object[]{from, to};
    }
}