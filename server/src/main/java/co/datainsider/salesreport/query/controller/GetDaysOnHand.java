package co.datainsider.salesreport.query.controller;

public class GetDaysOnHand extends QueryRequest {
    private String sku;
    private Long startDate;
    private Long endDate;

    public static final String GET_DAYS_ON_HAND = QueryTypes.GET_DAYS_ON_HAND;

    public GetDaysOnHand() {
        super(GET_DAYS_ON_HAND);
    }

    public GetDaysOnHand(String sku, Long startDate, Long endDate) {
        super(GET_DAYS_ON_HAND);
        this.sku = sku;
        this.startDate = startDate;
        this.endDate = endDate;
    }

    public String getSku() {
        return sku;
    }

    public void setSku(String sku) {
        this.sku = sku;
    }

    public Long getStartDate() {
        return startDate;
    }

    public void setStartDate(Long startDate) {
        this.startDate = startDate;
    }

    public Long getEndDate() {
        return endDate;
    }

    public void setEndDate(Long endDate) {
        this.endDate = endDate;
    }

    @Override
    public Object[] getParams() {
        return new Object[]{sku, startDate, endDate};
    }
}
