package co.datainsider.salesreport.query.controller;

public class GetSkuInventory extends QueryRequest {
    private Long startDate;
    private Long endDate;

    public GetSkuInventory() {
        super(QueryTypes.GET_SKU_INVENTORY);
    }

    public GetSkuInventory(Long startDate, Long endDate) {
        super(QueryTypes.GET_SKU_INVENTORY);
        this.startDate = startDate;
        this.endDate = endDate;
    }

    public Long getStartDate() {
        return startDate;
    }

    public void setStartDate(Long startDate) {
        this.startDate = startDate;
    }

    public Long getEndDate() {
        return endDate;
    }

    public void setEndDate(Long endDate) {
        this.endDate = endDate;
    }

    @Override
    public Object[] getParams() {
        return new Object[]{startDate, endDate};
    }
}