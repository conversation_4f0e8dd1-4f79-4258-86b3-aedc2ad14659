package co.datainsider.salesreport.query.controller;

public class GetDailyInventorySold extends QueryRequest {
    private String storeId;
    private String sku;
    private Long startDate;
    private Long endDate;

    public GetDailyInventorySold() {
        super(QueryTypes.GET_DAILY_INVENTORY_SOLD);
    }

    public GetDailyInventorySold(String storeId, String sku, Long startDate, Long endDate) {
        super(QueryTypes.GET_DAILY_INVENTORY_SOLD);
        this.storeId = storeId;
        this.sku = sku;
        this.startDate = startDate;
        this.endDate = endDate;
    }

    public String getStoreId() {
        return storeId;
    }

    public void setStoreId(String storeId) {
        this.storeId = storeId;
    }

    public String getSku() {
        return sku;
    }

    public void setSku(String sku) {
        this.sku = sku;
    }

    public Long getStartDate() {
        return startDate;
    }

    public void setStartDate(Long startDate) {
        this.startDate = startDate;
    }

    public Long getEndDate() {
        return endDate;
    }

    public void setEndDate(Long endDate) {
        this.endDate = endDate;
    }

    @Override
    public Object[] getParams() {
        return new Object[]{storeId, sku, startDate, endDate};
    }
}