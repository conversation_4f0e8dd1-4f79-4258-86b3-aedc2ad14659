SELECT sale_date                        AS saleDate,
       weather_condition                AS weatherCondition,
       voucher_program                  AS voucherProgram,
       store_id                         AS storeId,
       SUM(quantity_sold)               AS totalQuantitySold,
       SUM(opening_inventory)           AS totalOpeningInventory,
       toDayOfWeek(sale_date) IN (6, 7) AS isWeekend
FROM sales
WHERE store_id = ?
  AND sku = ?
  AND toUnixTimestamp(sale_date) BETWEEN ? AND ?
GROUP BY sale_date,
         weather_condition,
         voucher_program,
         store_id
ORDER BY store_id, sale_date;