WITH daily_metrics AS (
    SELECT
        store_id,
        anyLast(opening_inventory) as current_inventory,
        avg(quantity_sold) as avg_daily_sales
    FROM sales
    WHERE sku = ?
    AND toUnixTimestamp(sale_date) BETWEEN ? AND ?
    GROUP BY store_id
)
SELECT 
    store_id as storeId,
    current_inventory as currentInventory,
    if(avg_daily_sales > 0, round(current_inventory / avg_daily_sales, 2), 0) as daysOnHand
FROM daily_metrics
ORDER BY store_id;