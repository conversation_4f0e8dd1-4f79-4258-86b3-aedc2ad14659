DROP TABLE IF EXISTS sales;

-- Create sales table
CREATE TABLE IF NOT EXISTS sales
(
    sku               String,
    sale_date         Date,
    weather_condition String,
    voucher_program   String,
    store_id          String,
    quantity_sold     UInt32,
    opening_inventory UInt32
) ENGINE = MergeTree()
      ORDER BY (store_id, sale_date);

-- Insert sample sales data
INSERT INTO sales (sku, sale_date, weather_condition, voucher_program, store_id, quantity_sold, opening_inventory)
VALUES ('Laptop Pro X', '2024-01-01', 'Sunny', 'New Year Special', 'store_001', 5, 900),
       ('Coffee Maker Deluxe', '2024-01-01', 'Sunny', 'New Year Special', 'store_002', 8, 130),
       ('Smart Watch Elite', '2024-01-02', 'Cloudy', 'Weekend Deal', 'store_001', 12, 300),
       ('Wireless Earbuds', '2024-01-02', 'Rainy', 'Tech Tuesday', 'store_003', 15, 160),
       ('Gaming Console', '2024-01-03', 'Cloudy', 'None', 'store_002', 3, 500),
       ('Fitness Tracker', '2024-01-03', 'Sunny', 'Wellness Wednesday', 'store_001', 10, 90),
       ('Smart Speaker', '2024-01-04', 'Snowy', 'Winter Sale', 'store_003', 6, 130),
       ('Tablet Pro', '2024-01-04', 'Snowy', 'Winter Sale', 'store_002', 4, 700),
       ('Phone Charger', '2024-01-05', 'Clear', 'None', 'store_001', 25, 25),
       ('Bluetooth Speaker', '2024-01-05', 'Clear', 'Weekend Deal', 'store_003', 9, 80),
       ('4K TV', '2024-01-06', 'Cloudy', 'Weekend Sale', 'store_001', 3, 1300),
       ('Robot Vacuum', '2024-01-06', 'Cloudy', 'Smart Home Deal', 'store_002', 7, 450),
       ('Digital Camera', '2024-01-07', 'Sunny', 'Photo Week', 'store_003', 4, 600),
       ('Gaming Mouse', '2024-01-07', 'Sunny', 'Gaming Week', 'store_001', 20, 80),
       ('Air Purifier', '2024-01-08', 'Foggy', 'Health Sale', 'store_002', 6, 250),
       ('Wireless Keyboard', '2024-01-08', 'Foggy', 'Office Special', 'store_003', 15, 90),
       ('Coffee Beans Premium', '2024-01-09', 'Clear', 'None', 'store_001', 30, 20),
       ('Smart Doorbell', '2024-01-09', 'Clear', 'Smart Home Deal', 'store_002', 8, 180),
       ('External SSD', '2024-01-10', 'Rainy', 'Storage Sale', 'store_003', 12, 130),
       ('Protein Powder', '2024-01-10', 'Rainy', 'Fitness Friday', 'store_001', 18, 46),
       ('Electric Kettle', '2024-01-11', 'Sunny', 'Kitchen Deal', 'store_002', 9, 60),
       ('Yoga Mat', '2024-01-11', 'Sunny', 'Wellness Week', 'store_003', 25, 30),
       ('Desk Lamp', '2024-01-12', 'Cloudy', 'Home Office', 'store_001', 14, 40),
       ('Water Filter', '2024-01-12', 'Cloudy', 'None', 'store_002', 11, 50),
       ('Bluetooth Headphones', '2024-01-13', 'Snowy', 'Audio Week', 'store_003', 8, 160),
       ('Power Bank', '2024-01-13', 'Snowy', 'Travel Essentials', 'store_001', 22, 35),
       ('Smart Bulb Set', '2024-01-14', 'Clear', 'Smart Home Deal', 'store_002', 16, 80),
       ('Protein Bar Box', '2024-01-14', 'Clear', 'Fitness Deal', 'store_003', 35, 25),
       ('Monitor Stand', '2024-01-15', 'Foggy', 'Office Special', 'store_001', 7, 90),
       ('Hand Blender', '2024-01-15', 'Foggy', 'Kitchen Sale', 'store_002', 13, 46);