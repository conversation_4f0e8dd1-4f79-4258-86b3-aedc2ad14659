
// src/stores/sidebarStore.ts
import { defineStore } from 'pinia'

export interface TreeSelectNode {
  value: string
  title: string
  children?: TreeSelectNode[]
  selectable?: boolean
  disabled?: boolean
}

interface SidebarState {
  collapsed: boolean
  selectedFilters: string[]
  treeData: TreeSelectNode[]
  originalTreeData: TreeSelectNode[]
  searchValue: string
}

// Transform the provided JSON data to match TreeSelectNode interface
const productCategoriesData: TreeSelectNode[] = [
  {
    value: "1",
    title: "1.BAKERY AND DELICA (904)",
    children: [
      {
        value: "1.1",
        title: "2.BAKERY (595)"
      },
      {
        value: "1.2",
        title: "3.BREADS (292)"
      },
      {
        value: "1.3",
        title: "3.CAKES (163)"
      },
      {
        value: "1.4",
        title: "3.SEASONAL BAKERY (97)"
      },
      {
        value: "1.5",
        title: "3.<PERSON><PERSON><PERSON><PERSON> AND TREATS (43)"
      }
    ]
  },
  {
    value: "2",
    title: "4.<PERSON><PERSON><PERSON><PERSON> ẤN VẠT (43)",
    children: []
  },
  {
    value: "3",
    title: "5.BÁNH TRÁNG (25)",
    children: [
      {
        value: "3.1",
        title: "6.BÁNH TRÁNG (25)"
      },
      {
        value: "3.2",
        title: "5.BÁNH ÁP (5)"
      },
      {
        value: "3.3",
        title: "5.BÁNH ÁN VẠT KHÁC (12)"
      },
      {
        value: "3.4",
        title: "5.MÈ XỦN (1)"
      }
    ]
  },
  {
    value: "4",
    title: "2.DELICA (309)",
    children: []
  },
  {
    value: "5",
    title: "1.CHILLED AND FROZEN (871)",
    children: []
  },
  {
    value: "6",
    title: "1.DAIRY AND ICE CREAM (1563)",
    children: []
  },
  {
    value: "7",
    title: "1.FMCG (14046)",
    children: []
  },
  {
    value: "8",
    title: "1.FRESH FOOD (4533)",
    children: []
  },
  {
    value: "9",
    title: "1.HÀNG KHÔNG BÁN (1011)",
    children: []
  }
]

export const useSidebarStore = defineStore('sidebar', {
  state: (): SidebarState => ({
    collapsed: true,
    selectedFilters: [],
    treeData: productCategoriesData,
    originalTreeData: productCategoriesData,
    searchValue: ''
  }),

  getters: {
    isCollapsed: (state) => state.collapsed,
    getSelectedFilters: (state) => state.selectedFilters,
    getTreeData: (state) => state.treeData,
    getSearchValue: (state) => state.searchValue,

    // Get all available keys for select all functionality
    getAllKeys: (state) => {
      const getAllKeys = (nodes: TreeSelectNode[]): string[] => {
        let keys: string[] = []
        nodes.forEach(node => {
          keys.push(node.value)
          if (node.children && node.children.length > 0) {
            keys = keys.concat(getAllKeys(node.children))
          }
        })
        return keys
      }
      return getAllKeys(state.originalTreeData)
    },

    // Get filtered tree data based on search
    getFilteredTreeData: (state) => {
      if (!state.searchValue.trim()) {
        return state.originalTreeData
      }

      const filterTreeData = (nodes: TreeSelectNode[], searchTerm: string): TreeSelectNode[] => {
        const filteredNodes: TreeSelectNode[] = []

        nodes.forEach(node => {
          const nodeMatches = node.title.toLowerCase().includes(searchTerm.toLowerCase())
          let filteredChildren: TreeSelectNode[] = []

          if (node.children && node.children.length > 0) {
            filteredChildren = filterTreeData(node.children, searchTerm)
          }

          // Include node if it matches or has matching children
          if (nodeMatches || filteredChildren.length > 0) {
            filteredNodes.push({
              ...node,
              children: filteredChildren
            })
          }
        })

        return filteredNodes
      }

      return filterTreeData(state.originalTreeData, state.searchValue)
    }
  },

  actions: {
    toggleSidebar() {
      this.collapsed = !this.collapsed
    },

    updateSelectedFilters(filters: string[]) {
      this.selectedFilters = filters
    },

    selectAllFilters() {
      this.selectedFilters = this.getAllKeys
    },

    clearAllFilters() {
      this.selectedFilters = []
    },

    updateSearchValue(searchValue: string) {
      this.searchValue = searchValue
      this.treeData = this.getFilteredTreeData
    },

    resetSearch() {
      this.searchValue = ''
      this.treeData = this.originalTreeData
    },

    // Update tree data from external source (API call, etc.)
    updateTreeData(newTreeData: TreeSelectNode[]) {
      this.originalTreeData = newTreeData
      this.treeData = newTreeData
    },

    // Apply filters - can emit event or call API
    applyFilters() {
      console.log('Applying filters:', this.selectedFilters)
      // Add your API call or emit logic here
      return this.selectedFilters
    },

    // Cancel and reset all filters and search
    cancelFilters() {
      this.selectedFilters = []
      this.searchValue = ''
      this.treeData = this.originalTreeData
    },

    // Find node by value
    findNodeByValue(value: string): TreeSelectNode | null {
      const findNode = (nodes: TreeSelectNode[], targetValue: string): TreeSelectNode | null => {
        for (const node of nodes) {
          if (node.value === targetValue) {
            return node
          }
          if (node.children) {
            const found = findNode(node.children, targetValue)
            if (found) return found
          }
        }
        return null
      }

      return findNode(this.originalTreeData, value)
    },

    // Get selected node titles for display
    getSelectedNodeTitles(): string[] {
      return this.selectedFilters
        .map(value => this.findNodeByValue(value)?.title)
        .filter(Boolean) as string[]
    }
  }
})
