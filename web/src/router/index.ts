import { createRouter, createWebHistory } from 'vue-router'

const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),
  routes: [
    {
      path: '/',
      redirect: '/analytics',
    },
    {
      path: '/analytics',
      name: 'ChiaHangTuDong',
      redirect: '/analytics/replenishment',
      component: () => import('@/views/analytics/AnalyticsPage.vue'),
      children: [
        {
          path: 'inventory-detail',
          name: "InventoryDetail",
          component: () => import('@/views/analytics/views/inventory-detail/InventoryDetailPage.vue'),
        },

        {
          path: 'replenishment',
          name: "Replenishment",
          component: () => import('@/views/analytics/views/replenishment/ReplenishmentPage.vue'),
        },

      ]
    }
  ],
})

export default router
