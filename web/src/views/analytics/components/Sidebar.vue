<script setup lang="ts">
import { computed, ref, watch } from 'vue'
import { useSidebarStore } from '@/stores/sidebarStore'
import {
  SearchOutlined,
  CheckOutlined
} from '@ant-design/icons-vue'

const sidebarStore = useSidebarStore()

// Local state
const searchValue = ref('')
const expandedKeys = ref<string[]>([])
const checkedKeys = ref<string[]>([])

// Computed properties from store
const collapsed = computed(() => sidebarStore.isCollapsed)
const treeData = computed(() => sidebarStore.getFilteredTreeData)
const selectedFilters = computed(() => sidebarStore.getSelectedFilters)

// Sync checked keys with selected filters
watch(
  selectedFilters,
  (newValue) => {
    checkedKeys.value = newValue
  },
  { immediate: true }
)

// Watch search value and update store
watch(searchValue, (newValue) => {
  sidebarStore.updateSearchValue(newValue)
  // Auto expand all nodes when searching
  if (newValue.trim()) {
    expandAllNodes()
  }
})

// Methods
const onTreeCheck = (checkedKeysValue: string[] | { checked: string[]; halfChecked: string[] }) => {
  const keys = Array.isArray(checkedKeysValue) ? checkedKeysValue : checkedKeysValue.checked
  checkedKeys.value = keys
  sidebarStore.updateSelectedFilters(keys)
}

const onTreeExpand = (expandedKeysValue: string[]) => {
  expandedKeys.value = expandedKeysValue
}

const expandAllNodes = () => {
  const getAllKeys = (nodes: any[]): string[] => {
    let keys: string[] = []
    nodes.forEach((node) => {
      if (node.children && node.children.length > 0) {
        keys.push(node.value)
        keys = keys.concat(getAllKeys(node.children))
      }
    })
    return keys
  }
  expandedKeys.value = getAllKeys(treeData.value)
}

const selectAll = () => {
  sidebarStore.selectAllFilters()
}

const onApply = () => {
  const appliedFilters = sidebarStore.applyFilters()
  console.log('Applied filters:', appliedFilters)

  // Get selected node titles for better debugging
  const selectedTitles = sidebarStore.getSelectedNodeTitles()
  console.log('Selected categories:', selectedTitles)

  // You can emit an event or call parent method here
  // emit('filtersApplied', appliedFilters)
}

const onCancel = () => {
  sidebarStore.cancelFilters()
  searchValue.value = ''
  expandedKeys.value = []
}
</script>

<template>
  <ALayoutSider :collapsed="collapsed" class="app-sidebar" :width="320" :collapsedWidth="0">
    <div class="sidebar-container">
      <!-- Header -->
      <div class="sidebar-header">
        <div class="header-top">
          <ATypographyTitle :level="5">Product Categories</ATypographyTitle>
        </div>

        <div class="header-search">
          <AInput
            v-model:value="searchValue"
            placeholder="Search categories..."
            class="search-input"
            allow-clear
          >
            <template #prefix>
              <SearchOutlined />
            </template>
          </AInput>
        </div>

        <AFlex align="center" justify="space-between">
          <AButton type="link" size="small" @click="selectAll" class="action-btn">
            <template #icon>
              <CheckOutlined />
            </template>
            Select All
          </AButton>

          <AButton type="link" size="small" @click="expandAllNodes" class="control-btn">
            Expand All
          </AButton>
        </AFlex>
      </div>

      <!-- Tree Content -->
      <div class="sidebar-content">
        <div class="sidebar-tree">
          <ATree
            v-model:checkedKeys="checkedKeys"
            v-model:expandedKeys="expandedKeys"
            :tree-data="treeData"
            :field-names="{ children: 'children', title: 'title', key: 'value' }"
            checkable
            :check-strictly="false"
            :selectable="false"
            @check="onTreeCheck"
            @expand="onTreeExpand"
            class="category-tree"
          />

          <!-- Empty state when no results -->
          <div v-if="treeData.length === 0 && searchValue.trim()" class="empty-state">
            <AEmpty description="No categories found" />
          </div>
        </div>
      </div>

      <!-- Footer -->
      <div class="sidebar-footer">
        <AFlex gap="small" style="width: 100%">
          <AButton block @click="onCancel" class="cancel-btn">Cancel</AButton>
          <AButton
            type="primary"
            block
            @click="onApply"
            class="apply-btn"
            :disabled="selectedFilters.length === 0"
          >
            Apply ({{ selectedFilters.length }})
          </AButton>
        </AFlex>
      </div>
    </div>
  </ALayoutSider>
</template>

<style scoped>

.app-sidebar {
  --sidebar-width: 320px;
  --sidebar-collapsed-width: 0px;
  --header-height: 48px;
  --border-color: rgba(234, 236, 240, 1);
  --sidebar-z-index: 100;
  --sidebar-padding: 1rem;
  --sidebar-padding-small: 0.75rem;
  --border-radius: 8px;
  --transition-duration: 0.3s;
  --transition-fast: 0.2s;
  --scrollbar-width: 6px;
  --scrollbar-border-radius: 3px;
  --tree-node-padding: 2px 0;
  --tree-content-padding: 4px 8px;
  --tree-border-radius: 4px;
  --tree-checkbox-margin: 8px;
  --tree-switcher-size: 20px;
  --tree-indent-level-2: 8px;
  --tree-indent-level-3: 16px;
  --empty-state-padding: 2rem;
  --mobile-breakpoint: 768px;
  --mobile-title-size: 1.1rem;


  transition: all var(--transition-duration) ease;
  height: 100vh;
  position: fixed;
  left: 0;
  top: var(--header-height);
  background-color: white;
  border-right: 1px solid var(--border-color);
  z-index: var(--sidebar-z-index);
  overflow: hidden;
}

.sidebar-container {
  display: flex;
  flex-direction: column;
  height: calc(100% - var(--header-height));
  min-height: 0;
}

/* Header Styles */
.sidebar-header {
  padding: var(--sidebar-padding);
  border-bottom: 1px solid var(--border-color);
  background-color: #fff;
  flex-shrink: 0;
}

.header-top {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: var(--sidebar-padding);
}

.header-search {
  margin-bottom: var(--sidebar-padding-small);
}

.search-input {
  border-radius: var(--border-radius);
}

.action-btn {
  padding: 0;
  height: auto;
  font-size: 12px;
  color: #1890ff;
}

.action-btn:hover {
  color: #40a9ff;
}

/* Tree Controls */
.control-btn {
  padding: 0;
  height: auto;
  font-size: 11px;
  color: #666;
}

.control-btn:hover {
  color: #1890ff;
}

/* Content Styles */
.sidebar-content {
  flex: 1;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  min-height: 0;
}

.sidebar-tree {
  padding: var(--sidebar-padding);
  min-width: 280px;
  overflow-y: auto;
  overflow-x: auto;
  flex: 1;
  scrollbar-width: thin;
  scrollbar-color: #c1c1c1 transparent;
}

.sidebar-tree::-webkit-scrollbar {
  width: var(--scrollbar-width);
  height: var(--scrollbar-width);
}

.sidebar-tree::-webkit-scrollbar-track {
  background: transparent;
}

.sidebar-tree::-webkit-scrollbar-thumb {
  background-color: #c1c1c1;
  border-radius: var(--scrollbar-border-radius);
}

.sidebar-tree::-webkit-scrollbar-thumb:hover {
  background-color: #a1a1a1;
}

.category-tree {
  background: transparent;
}

/* Tree customization */
:deep(.ant-tree .ant-tree-treenode) {
  padding: var(--tree-node-padding);
}

:deep(.ant-tree .ant-tree-node-content-wrapper) {
  padding: var(--tree-content-padding);
  border-radius: var(--tree-border-radius);
  transition: all var(--transition-fast);
}

:deep(.ant-tree .ant-tree-node-content-wrapper:hover) {
  background-color: #f5f5f5;
}

:deep(.ant-tree .ant-tree-title) {
  font-size: 13px;
  line-height: 1.4;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

:deep(.ant-tree .ant-tree-checkbox) {
  margin-right: var(--tree-checkbox-margin);
}

:deep(.ant-tree .ant-tree-switcher) {
  width: var(--tree-switcher-size);
  height: var(--tree-switcher-size);
  line-height: var(--tree-switcher-size);
}

/* Different colors for different levels */
:deep(.ant-tree .ant-tree-treenode[aria-level='1'] .ant-tree-title) {
  font-weight: 600;
  color: #262626;
}

:deep(.ant-tree .ant-tree-treenode[aria-level='2'] .ant-tree-title) {
  font-weight: 500;
  color: #595959;
}

:deep(.ant-tree .ant-tree-treenode[aria-level='3'] .ant-tree-title) {
  color: #8c8c8c;
}

/* Indent lines for better visual hierarchy */
:deep(.ant-tree .ant-tree-treenode[aria-level='2']) {
  margin-left: var(--tree-indent-level-2);
  border-left: 1px dashed #e8e8e8;
  padding-left: var(--tree-indent-level-2);
}

:deep(.ant-tree .ant-tree-treenode[aria-level='3']) {
  margin-left: var(--tree-indent-level-3);
  border-left: 1px dashed #e8e8e8;
  padding-left: var(--tree-indent-level-2);
}

.empty-state {
  padding: var(--empty-state-padding);
  text-align: center;
}

/* Footer Styles */
.sidebar-footer {
  padding: var(--sidebar-padding);
  border-top: 1px solid var(--border-color);
  background-color: #fafafa;
  flex-shrink: 0;
}

.cancel-btn {
  border-color: #d9d9d9;
  color: #595959;
}

.cancel-btn:hover {
  border-color: #40a9ff;
  color: #40a9ff;
}

.apply-btn {
  background-color: #1890ff;
  border-color: #1890ff;
}

.apply-btn:hover:not(:disabled) {
  background-color: #40a9ff;
  border-color: #40a9ff;
}

.apply-btn:disabled {
  background-color: #f5f5f5;
  border-color: #d9d9d9;
  color: #bfbfbf;
}

/* Animation for collapse */
.app-sidebar.ant-layout-sider-collapsed {
  width: 0 !important;
  min-width: 0 !important;
  max-width: 0 !important;
  flex: 0 0 0 !important;
}

/* Responsive adjustments */
@media (max-width: var(--mobile-breakpoint)) {
  .sidebar-title {
    font-size: var(--mobile-title-size);
  }

  .sidebar-header,
  .sidebar-tree,
  .sidebar-footer {
    padding: var(--sidebar-padding-small);
  }
}
</style>
