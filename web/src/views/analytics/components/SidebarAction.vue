<script setup lang="ts">
import {
  MenuOutlined,
  FilterOutlined,
  EyeOutlined,
  SettingOutlined,
  AppstoreOutlined
} from '@ant-design/icons-vue'
import { computed, h } from 'vue'
import { useSidebarStore } from '@/stores/sidebarStore'

const sidebarStore = useSidebarStore()

const toggleSidebar = () => {
  sidebarStore.toggleSidebar()
}
const menuButtonType = computed(() => {
  return sidebarStore.isCollapsed ? 'text' : 'primary'
})

</script>

<template>
  <AFlex align="center" justify="space-between" class="sidebar-action">
    <AFlex align="center" gap="small">
      <AButton
        :type="menuButtonType"
        :icon="h(MenuOutlined)"
        @click="toggleSidebar"
      />
      <AButton type="text" :icon="h(FilterOutlined)" />
    </AFlex>

    <AFlex align="center" gap="small">
      <AButton type="text" :icon="h(EyeOutlined)" />
      <AButton type="text" :icon="h(SettingOutlined)" />
      <AButton type="primary" :icon="h(AppstoreOutlined)" />
    </AFlex>
  </AFlex>
</template>

<style>
.sidebar-action {
  border-bottom: 1px solid rgba(234, 236, 240, 1);
  padding: 0.5rem 1rem;
}
</style>
