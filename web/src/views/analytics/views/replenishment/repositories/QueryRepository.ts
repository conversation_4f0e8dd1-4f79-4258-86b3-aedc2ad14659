import type {
  GetDailyInventorySoldRequest,
  GetDayOnHandRequest,
  GetSalesRequest,
  GetSkuListingRequest
} from '@/views/analytics/views/replenishment/domains/requests/GetSalesRequest.ts'
import { type HttpClient } from '@/client/HttpClient.ts'
import type {
  DailyInventorySoldRow, DayOnHandRow,
  GetDailyInventorySoldResponse,
  GetDayOnHandResponse,
  GetSalesResponse,
  SkuListingResponse
} from '@/views/analytics/views/replenishment/domains/responses/GetSalesResponse.ts'
import client from '@/client/HttpClientBuilder.ts'

export abstract class QueryRepository {
  abstract getSales(request: GetSalesRequest): Promise<GetSalesResponse>

  abstract getDayOnHand(request: GetDayOnHandRequest): Promise<GetDayOnHandResponse>

  abstract getSkuListing(request: GetSkuListingRequest): Promise<SkuListingResponse>

  abstract getDailyInventorySold(request: GetDailyInventorySoldRequest): Promise<GetDailyInventorySoldResponse>
}

export class QueryRepositoryImpl extends QueryRepository {
  readonly client: HttpClient

  constructor(client: HttpClient) {
    super()
    this.client = client
  }

  async getSales(request: GetSalesRequest): Promise<GetSalesResponse> {
    return this.client
      .post<GetSalesResponse>('/api/query', {
        ...request,
        name: 'get_sales_volume'
      })
      .then((response) => response.data)
  }

  async getDayOnHand(request: GetDayOnHandRequest): Promise<GetDayOnHandResponse> {
    return new MockDataGenerator().getDayOnHand(request)
    // return this.client
    //   .post<GetDayOnHandResponse>('/api/query', {
    //     ...request,
    //     name: 'get_days_on_hand'
    //   })
    //   .then((response) => response.data)
  }

  async getSkuListing(request: GetSkuListingRequest): Promise<SkuListingResponse> {
    const res = await this.client
      .post<SkuListingResponse>('/api/query', {
        ...request,
        name: 'get_sku_inventory'
      })
    return res.data
  }


  async getDailyInventorySold(request: GetDailyInventorySoldRequest): Promise<GetDailyInventorySoldResponse> {
    return new MockDataGenerator().getDailyInventorySold(request)
    // const res = await this.client
    //   .post<GetDailyInventorySoldResponse>('/api/query', {
    //     ...request,
    //     name: 'get_daily_inventory_sold'
    //   })
    // return res.data
  }


}

export default new QueryRepositoryImpl(client)


class MockDataGenerator {

  /**
   * Mock data cho hàm getDayOnHand
   */
  async getDayOnHand(request: GetDayOnHandRequest): Promise<GetDayOnHandResponse> {
    console.log('🚀 Bắt đầu generate mock data cho getDayOnHand...')

    // Delay 20 giây
    await this.delay(1000)

    const mockRows: DayOnHandRow[] = []
    const storeIds = this.generateStoreIds(20) // 20 cửa hàng khác nhau

    // Tạo 120 rows
    for (let i = 0; i < 120; i++) {
      const storeId = storeIds[i % storeIds.length]
      mockRows.push({
        currentInventory: this.randomBetween(50, 500),
        storeId: storeId,
        daysOnHand: this.randomBetween(1, 30)
      })
    }

    console.log(`✅ Đã generate ${mockRows.length} rows cho getDayOnHand`)

    return {
      rows: mockRows
    }
  }

  /**
   * Mock data cho hàm getDailyInventorySold
   */
  async getDailyInventorySold(request: GetDailyInventorySoldRequest): Promise<GetDailyInventorySoldResponse> {
    console.log('🚀 Bắt đầu generate mock data cho getDailyInventorySold...')

    // Delay 20 giây
    await this.delay(1000)

    const mockRows: DailyInventorySoldRow[] = []
    const startDate = new Date(request.startDate * 1000)
    const endDate = new Date(request.endDate * 1000)

    // Tạo dữ liệu theo ngày từ startDate đến endDate, repeat để đạt 120 rows
    let currentDate = new Date(startDate)
    let rowCount = 0

    while (rowCount < 120) {
      if (currentDate > endDate) {
        currentDate = new Date(startDate) // Reset về startDate
      }

      const inventory = this.randomBetween(100, 1000)
      const sold = this.randomBetween(10, 100)
      const doh = Math.round(inventory / sold)

      mockRows.push({
        date: this.formatDate(currentDate),
        sold: sold,
        inventory: inventory,
        doh: doh,
        forecastSold: this.randomBetween(sold - 10, sold + 10),
        request: this.randomBetween(sold + 5, sold + 20)
      })

      currentDate.setDate(currentDate.getDate() + 1)
      rowCount++
    }

    console.log(`✅ Đã generate ${mockRows.length} rows cho getDailyInventorySold`)

    return {
      rows: mockRows
    }
  }

  /**
   * Tạo delay
   */
  private delay(ms: number): Promise<void> {
    return new Promise(resolve => {
      console.log(`⏳ Đang delay ${ms/1000} giây để test hiệu năng...`)
      setTimeout(resolve, ms)
    })
  }

  /**
   * Tạo random number trong khoảng min-max
   */
  private randomBetween(min: number, max: number): number {
    return Math.floor(Math.random() * (max - min + 1)) + min
  }

  /**
   * Tạo danh sách store IDs
   */
  private generateStoreIds(count: number): string[] {
    const storeIds: string[] = []
    for (let i = 1; i <= count; i++) {
      storeIds.push(`STORE_${i.toString().padStart(3, '0')}`)
    }
    return storeIds
  }

  /**
   * Format date thành string YYYY-MM-DD
   */
  private formatDate(date: Date): string {
    return date.toISOString().split('T')[0]
  }
}
