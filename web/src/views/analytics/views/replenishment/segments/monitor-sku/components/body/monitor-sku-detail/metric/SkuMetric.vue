<script setup lang="ts">
import { computed, inject, provide } from 'vue'
import MetricTable from '@/views/analytics/views/replenishment/segments/monitor-sku/components/body/monitor-sku-detail/metric/MetricTable.vue'
import type {
  useSkuMetricStore
} from '@/views/analytics/views/replenishment/segments/monitor-sku/stores/SkuMetricStore.ts'
import { DateRange } from '@/views/analytics/views/replenishment/segments/monitor-sku/stores/DateRangeCalculator.ts'
// Props
interface Props {
  productId: string
  storeId: string
  dateRange: DateRange
}

const props = defineProps<Props>()

// Inject store từ parent component
const skuMetricStore = inject<ReturnType<typeof useSkuMetricStore>>('skuMetricStore')

if (!skuMetricStore) {
  throw new Error('skuMetricStore not provided')
}

// Provide skuMetricStore cho MetricTable
provide('skuMetricStore', skuMetricStore)

// Computed cho SKU metric hiện tại
const currentSkuMetric = computed(() => {
  return skuMetricStore.getCachedData(props.productId, props.storeId, props.dateRange)
})

const isLoading = computed(() => {
  return skuMetricStore.isLoadingFor(props.productId, props.storeId, props.dateRange)
})

const errorMessage = computed(() => {
  return skuMetricStore.getErrorFor(props.productId, props.storeId, props.dateRange)
})

const handleClearError = () => {
  if (props.productId && props.storeId) {
    skuMetricStore.clearError(props.productId, props.storeId, props.dateRange)
  }
}

</script>

<template>
  <AFlex vertical gap="small">
    <AFlex justify="space-between" align="center">
      <ATypographyTitle :level="5">Metric Detail</ATypographyTitle>

    </AFlex>

    <ASpin :spinning="isLoading">
      <!-- Hiển thị error message nếu có -->
      <AAlert
        v-if="errorMessage"
        type="error"
        :message="errorMessage"
        show-icon
        closable
        @close="handleClearError"
        style="margin-bottom: 16px"
      />

      <!-- Hiển thị empty state khi không có data -->
      <AEmpty
        v-else-if="!isLoading && !currentSkuMetric"
        description="Không có dữ liệu metric"
      />

      <!-- Hiển thị MetricTable khi có data -->
      <MetricTable
        v-else-if="currentSkuMetric"
        :metric-data="currentSkuMetric"
        :product-id="productId"
        :store-id="storeId"
      />
    </ASpin>
  </AFlex>

</template>

<style scoped></style>
