<script setup lang="ts">
import { watch, onMounted, provide, inject, ref } from 'vue'
import MonitorMetricSegment from '@/views/analytics/views/replenishment/segments/monitor-metric/MonitorMetricSegment.vue'
import SkuDetail from '@/views/analytics/views/replenishment/segments/monitor-sku/components/body/monitor-sku-detail/detail/SkuDetail.vue'
import SkuMetric from '@/views/analytics/views/replenishment/segments/monitor-sku/components/body/monitor-sku-detail/metric/SkuMetric.vue'
import ForecastDetail
  from '@/views/analytics/views/replenishment/segments/monitor-sku/components/body/monitor-sku-detail/forecast-chart/ForecastDetail.vue'
import { DateRange } from '@/views/analytics/views/replenishment/segments/monitor-sku/stores/DateRangeCalculator.ts'
import type {
  useSkuDetailStore
} from '@/views/analytics/views/replenishment/segments/monitor-sku/stores/SkuDetailStore.ts'
import { useSkuMetricStore } from '@/views/analytics/views/replenishment/segments/monitor-sku/stores/SkuMetricStore.ts'
import type {
  useSkuForecastStore
} from '@/views/analytics/views/replenishment/segments/monitor-sku/stores/SkuForecastStore.ts'

// Props
interface Props {
  productId: string
  storeId?: string
}

const props = withDefaults(defineProps<Props>(), {
  storeId: 'store_001' // Default store ID
})

// Tạo ref value cho dateRange với enum
const dateRange = ref<DateRange>(DateRange.LAST_7_DAYS)

const skuDetailStore = inject<ReturnType<typeof useSkuDetailStore>>('skuDetailStore')
if (!skuDetailStore) {
  throw new Error('skuDetailStore not provided')
}

const skuMetricStore = inject<ReturnType<typeof useSkuMetricStore>>('skuMetricStore')
if (!skuMetricStore) {
  throw new Error('skuMetricStore not provided')
}

const skuForecastStore = inject<ReturnType<typeof useSkuForecastStore>>('skuForecastStore')
if (!skuForecastStore) {
  throw new Error('skuForecastStore not provided')
}

// Function để fetch data từ 3 stores
const fetchAllData = async (productId: string, storeId: string, dateRangeValue: DateRange) => {
  if (productId && storeId) {
    await Promise.all([
      skuDetailStore.fetchSkuDetail(productId, dateRangeValue),
      skuMetricStore.fetchSkuMetric(productId, storeId, dateRangeValue),
      skuForecastStore.fetchForecastData(productId, storeId, dateRangeValue)
    ])
  }
}

onMounted(async () => {
  await fetchAllData(props.productId, props.storeId, dateRange.value)
})

watch(
  () => [props.productId, props.storeId, dateRange.value] as const,
  async ([newProductId, newStoreId, newDateRange]) => {
    console.log('newProductId', newDateRange)
    await fetchAllData(newProductId, newStoreId, newDateRange)
  },
  { deep: true }
)
</script>

<template>
  <AFlex vertical gap="middle">
    <MonitorMetricSegment
      v-model:date-range="dateRange"
    />
    <SkuDetail :productId="productId" :date-range="dateRange"/>
    <ForecastDetail :productId="productId" :storeId="storeId" :date-range="dateRange"/>
    <SkuMetric :productId="productId" :storeId="storeId" :date-range="dateRange"/>
  </AFlex>
</template>

<style>

</style>
