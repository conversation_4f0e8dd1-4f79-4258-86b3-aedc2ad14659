import type {
  ForecastCategory,
  ForecastData,
  GetSalesResponse, SaleRow, SeriesDataItem
} from '@/views/analytics/views/replenishment/domains/responses/GetSalesResponse.ts'

export class SalesResponseAdapter {
  toForecastData(response: GetSalesResponse): ForecastData {
    const { rows } = response;

    const categories: ForecastCategory[] = this.toCategories(rows);
    const saleVolumes = this.toSaleVolumes(rows);
    const saleVolumesPredict = this.toSaleVolumesPredict(rows);
    const voucherPrograms = this.toVoucherPrograms(rows)
    const seriesData = this.toSeriesData(rows);

    return {
      categories,
      saleVolumes,
      saleVolumesPredict,
      voucherPrograms,
      seriesData
    }
  }

  private toCategories(rows: SaleRow[]): ForecastCategory[] {
    return rows.map(row => {
      return {
        date: row.saleDate,
        isWeekend: row.isWeekend,
        weatherCondition: row.weatherCondition,
        forecast: false
      }
    })
  }

  private toSaleVolumes(rows: SaleRow[]): number[]{
    return rows.map(row => row.totalQuantitySold)
  }

  private toSaleVolumesPredict(_: SaleRow[]): number[]{
    return []
  }

  private toVoucherPrograms(rows: SaleRow[]): string[]{
    return rows.map(row => row.voucherProgram)
  }

  private toSeriesData(rows: SaleRow[]): SeriesDataItem[]{
    return rows.map((row) => {
      return {
        quantity: row.totalOpeningInventory,
        skuPerDay: 0
      }
    })
  }
}
