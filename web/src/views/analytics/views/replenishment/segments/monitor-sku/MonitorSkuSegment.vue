
<script setup lang="ts">
import { onMounted, provide, watch } from 'vue'
import { useSKUStore } from './stores/SkuStore.ts'
import { DateRange, DateRangeLabels } from './stores/DateRangeCalculator'
import MonitorSkuSegmentHeader from '@/views/analytics/views/replenishment/segments/monitor-sku/components/MonitorSkuSegmentHeader.vue'
import MonitorSkuTable from '@/views/analytics/views/replenishment/segments/monitor-sku/components/body/MonitorSkuTable.vue'
import { useSkuDetailStore } from '@/views/analytics/views/replenishment/segments/monitor-sku/stores/SkuDetailStore.ts'
import { useSkuMetricStore } from '@/views/analytics/views/replenishment/segments/monitor-sku/stores/SkuMetricStore.ts'
import {
  useSkuForecastStore
} from '@/views/analytics/views/replenishment/segments/monitor-sku/stores/SkuForecastStore.ts'
type Props = {
  dateRange?: DateRange
}
const props = withDefaults(defineProps<Props>(), {
  dateRange: DateRange.LAST_7_DAYS,
})

const skuStore = useSKUStore()
const skuDetailStore = useSkuDetailStore()
const skuMetricStore = useSkuMetricStore()
const skuForecastStore = useSkuForecastStore()

// Provide stores cho các component con
provide('skuDetailStore', skuDetailStore)
provide('skuMetricStore', skuMetricStore)
provide('skuForecastStore', skuForecastStore)

onMounted(() => {
  // Load initial data
  skuStore.fetchSkuList()
})

watch(
  () => [props.dateRange] as const,
  async ([newDateRange]) => {
    await skuStore.fetchSkuList(newDateRange)
  },
  { deep: true }
)

</script>

<template>
  <AFlex vertical>
    <MonitorSkuSegmentHeader />
    <!-- SKU Table -->
    <MonitorSkuTable
      :records="skuStore.skuList"
      :loading="skuStore.loading"
      :error="skuStore.error"
    />
  </AFlex>
</template>
