import type { SkuListingResponse } from '@/views/analytics/views/replenishment/domains/responses/GetSalesResponse.ts'
import type { GetSkuListingRequest } from '@/views/analytics/views/replenishment/domains'
import type { SKUModel } from '@/views/analytics/views/replenishment/segments/monitor-sku/components/body/models/SKU.model.ts'
import repository, {
  type QueryRepository,
} from '@/views/analytics/views/replenishment/repositories/QueryRepository.ts'
import { SkuListingResponseAdapter } from '@/views/analytics/views/replenishment/segments/monitor-sku/core/helpers/SkuListingResponseAdapter.ts'

export abstract class SkuService {
  abstract getSkuListing(request: GetSkuListingRequest): Promise<SKUModel[]>
}

export class SkuServiceImpl extends SkuService {
  private readonly repository: QueryRepository

  constructor(repository: QueryRepository) {
    super()
    this.repository = repository
  }

  async getSkuListing(request: GetSkuListingRequest): Promise<SKUModel[]> {
    const response = await this.repository.getSkuListing(request)
    return SkuListingResponseAdapter.adapt(response)
  }
}

export default new SkuServiceImpl(repository)
