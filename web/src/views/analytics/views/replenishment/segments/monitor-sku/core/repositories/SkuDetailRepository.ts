import {
  type PivotTableData,
  SkuDetailSchema,
} from '@/views/analytics/views/replenishment/segments/monitor-sku/core/domains/PivotTableData.ts'

export abstract class SkuDetailRepository {
  abstract getSkuDetail(): Promise<PivotTableData>

  abstract getMetricDetail(): Promise<PivotTableData>
}

export class MockSkuDetailRepository extends SkuDetailRepository {
  async getSkuDetail(): Promise<PivotTableData> {
    return Promise.resolve(
      SkuDetailSchema.parse({
        'dayAxis': [
          '26/4',
          '26/4',
          '26/4',
          '26/4',
          '26/4',
          '26/4',
          '26/4',
          '26/4',
          '26/4',
          '26/4',
          '26/4',

          '26/4',
          '26/4',
          '26/4',
          '26/4',
          '26/4',
          '26/4',
          '26/4',
          '26/4',
          '26/4',
          '26/4',
        ],
        'tableData': [
          {
            id: '1',
            metricName: 'Inventory quantity',
            storeInfos: [
              { name: 'KHO SEEDLOG', value: 30, status: 'default' },
              { name: 'Store 1', value: 30, status: 'default' },
              { name: 'Store 2', value: 30, status: 'default' },
              { name: 'Store 3', value: 30, status: 'default' },
              { name: 'Store 4', value: 30, status: 'default' },
              { name: 'Store 5', value: 30, status: 'default' },
              { name: 'Store 6', value: 30, status: 'default' },
              { name: 'Store 7', value: 30, status: 'default' },
              { name: 'Store 8', value: 30, status: 'default' },
              { name: 'Store 9', value: 30, status: 'default' },
              { name: 'Store 10', value: 30, status: 'default' },

              { name: 'Store 11', value: 30, status: 'default' },
              { name: 'Store 12', value: 30, status: 'default' },
              { name: 'Store 13', value: 30, status: 'default' },
              { name: 'Store 14', value: 30, status: 'default' },
              { name: 'Store 15', value: 30, status: 'default' },
              { name: 'Store 16', value: 30, status: 'default' },
              { name: 'Store 17', value: 30, status: 'default' },
              { name: 'Store 18', value: 30, status: 'default' },
              { name: 'Store 19', value: 30, status: 'default' },
              { name: 'Store 20', value: 30, status: 'default' },
            ],
          },
          {
            id: '2',
            metricName: 'Days on hand',
            storeInfos: [
              { name: 'KHO SEEDLOG', value: 8, status: 'warning' },
              { name: 'Store 1', value: 2, status: 'error' },
              { name: 'Store 2', value: 3, status: 'normal' },
              { name: 'Store 3', value: 3, status: 'normal' },
              { name: 'Store 4', value: 2, status: 'error' },
              { name: 'Store 5', value: 6, status: 'warning' },
              { name: 'Store 6', value: 7, status: 'warning' },
              { name: 'Store 7', value: 9, status: 'normal' },
              { name: 'Store 8', value: 9, status: 'normal' },
              { name: 'Store 9', value: 9, status: 'normal' },
              { name: 'Store 10', value: 9, status: 'normal' },

              { name: 'Store 11', value: 30, status: 'default' },
              { name: 'Store 12', value: 30, status: 'default' },
              { name: 'Store 13', value: 30, status: 'default' },
              { name: 'Store 14', value: 30, status: 'default' },
              { name: 'Store 15', value: 30, status: 'default' },
              { name: 'Store 16', value: 30, status: 'default' },
              { name: 'Store 17', value: 30, status: 'default' },
              { name: 'Store 18', value: 30, status: 'default' },
              { name: 'Store 19', value: 30, status: 'default' },
              { name: 'Store 20', value: 30, status: 'default' },
            ],
          },
          {
            id: '3',
            metricName: 'On-Order',
            storeInfos: [
              { name: 'KHO SEEDLOG', value: 20, status: 'warning' },
              { name: 'Store 1', value: 10, status: 'normal' },
              { name: 'Store 2', value: 2, status: 'error' },
              { name: 'Store 3', value: 2, status: 'error' },
              { name: 'Store 4', value: 1, status: 'error' },
              { name: 'Store 5', value: 12, status: 'normal' },
              { name: 'Store 6', value: 22, status: 'normal' },
              { name: 'Store 7', value: 22, status: 'normal' },
              { name: 'Store 8', value: 11, status: 'normal' },
              { name: 'Store 9', value: 33, status: 'normal' },
              { name: 'Store 10', value: 1, status: 'error' },

              { name: 'Store 11', value: 30, status: 'default' },
              { name: 'Store 12', value: 30, status: 'default' },
              { name: 'Store 13', value: 30, status: 'default' },
              { name: 'Store 14', value: 30, status: 'default' },
              { name: 'Store 15', value: 30, status: 'default' },
              { name: 'Store 16', value: 30, status: 'default' },
              { name: 'Store 17', value: 30, status: 'default' },
              { name: 'Store 18', value: 30, status: 'default' },
              { name: 'Store 19', value: 30, status: 'default' },
              { name: 'Store 20', value: 30, status: 'default' },
            ],
          },
        ],
      }),
    )
  }

  getMetricDetail(): Promise<PivotTableData> {
    return Promise.resolve(
      SkuDetailSchema.parse({
        'dayAxis': [
          '01/01/2025', '02/01/2025', '03/01/2025', '04/01/2025', '05/01/2025', '06/01/2025',
          '07/01/2025', '08/01/2025', '09/01/2025', '10/01/2025', '11/01/2025', '12/01/2025',
          '13/01/2025', '14/01/2025', '15/01/2025', '16/01/2025', '17/01/2025', '18/01/2025',
          '19/01/2025', '20/01/2025', '21/01/2025'
        ],
        'tableData': [
          {
            id: '1',
            metricName: 'Inventory quantity',
            storeInfos: [
              { name: '01/01/2025', value: 30, status: 'default' },
              { name: '02/01/2025', value: 30, status: 'default' },
              { name: '03/01/2025', value: 30, status: 'default' },
              { name: '04/01/2025', value: 30, status: 'default' },
              { name: '05/01/2025', value: 30, status: 'default' },
              { name: '06/01/2025', value: 30, status: 'default' },
              { name: '07/01/2025', value: 30, status: 'default' },
              { name: '08/01/2025', value: 30, status: 'default' },
              { name: '09/01/2025', value: 30, status: 'default' },
              { name: '10/01/2025', value: 30, status: 'default' },
              { name: '11/01/2025', value: 30, status: 'default' },
              { name: '12/01/2025', value: 30, status: 'default' },
              { name: '13/01/2025', value: 30, status: 'default' },
              { name: '14/01/2025', value: 30, status: 'default' },
              { name: '15/01/2025', value: 30, status: 'default' },
              { name: '16/01/2025', value: 30, status: 'default' },
              { name: '17/01/2025', value: 30, status: 'default' },
              { name: '18/01/2025', value: 30, status: 'default' },
              { name: '19/01/2025', value: 30, status: 'default' },
              { name: '20/01/2025', value: 30, status: 'default' },
              { name: '21/01/2025', value: 30, status: 'default' }
            ]
          },
          {
            id: '2',
            metricName: 'DOH',
            storeInfos: [
              { name: '01/01/2025', value: 150, status: 'default' },
              { name: '02/01/2025', value: 150, status: 'default' },
              { name: '03/01/2025', value: 150, status: 'default' },
              { name: '04/01/2025', value: 150, status: 'default' },
              { name: '05/01/2025', value: 150, status: 'default' },
              { name: '06/01/2025', value: 150, status: 'default' },
              { name: '07/01/2025', value: 150, status: 'default' },
              { name: '08/01/2025', value: 150, status: 'default' },
              { name: '09/01/2025', value: 150, status: 'default' },
              { name: '10/01/2025', value: 150, status: 'default' },
              { name: '11/01/2025', value: 150, status: 'default' },
              { name: '12/01/2025', value: 150, status: 'default' },
              { name: '13/01/2025', value: 150, status: 'default' },
              { name: '14/01/2025', value: 150, status: 'default' },
              { name: '15/01/2025', value: 150, status: 'default' },
              { name: '16/01/2025', value: 150, status: 'default' },
              { name: '17/01/2025', value: 150, status: 'default' },
              { name: '18/01/2025', value: 150, status: 'default' },
              { name: '19/01/2025', value: 150, status: 'default' },
              { name: '20/01/2025', value: 150, status: 'default' },
              { name: '21/01/2025', value: 150, status: 'default' }
            ]
          },
          {
            id: '3',
            metricName: 'Số lần thực tế',
            storeInfos: [
              { name: '01/01/2025', value: 25, status: 'default' },
              { name: '02/01/2025', value: 25, status: 'default' },
              { name: '03/01/2025', value: 25, status: 'default' },
              { name: '04/01/2025', value: 25, status: 'default' },
              { name: '05/01/2025', value: 25, status: 'default' },
              { name: '06/01/2025', value: 25, status: 'default' },
              { name: '07/01/2025', value: 25, status: 'default' },
              { name: '08/01/2025', value: 25, status: 'default' },
              { name: '09/01/2025', value: 25, status: 'default' },
              { name: '10/01/2025', value: 25, status: 'default' },
              { name: '11/01/2025', value: 25, status: 'default' },
              { name: '12/01/2025', value: 25, status: 'default' },
              { name: '13/01/2025', value: 25, status: 'default' },
              { name: '14/01/2025', value: 25, status: 'default' },
              { name: '15/01/2025', value: 25, status: 'default' },
              { name: '16/01/2025', value: 25, status: 'default' },
              { name: '17/01/2025', value: 25, status: 'default' },
              { name: '18/01/2025', value: 25, status: 'default' },
              { name: '19/01/2025', value: 25, status: 'default' },
              { name: '20/01/2025', value: 25, status: 'default' },
              { name: '21/01/2025', value: 25, status: 'default' }
            ]
          },
          {
            id: '4',
            metricName: 'Số lần dự đoán',
            storeInfos: [
              { name: '01/01/2025', value: 150, status: 'default' },
              { name: '02/01/2025', value: 150, status: 'default' },
              { name: '03/01/2025', value: 150, status: 'default' },
              { name: '04/01/2025', value: 150, status: 'default' },
              { name: '05/01/2025', value: 150, status: 'default' },
              { name: '06/01/2025', value: 150, status: 'default' },
              { name: '07/01/2025', value: 150, status: 'default' },
              { name: '08/01/2025', value: 150, status: 'default' },
              { name: '09/01/2025', value: 150, status: 'default' },
              { name: '10/01/2025', value: 150, status: 'default' },
              { name: '11/01/2025', value: 150, status: 'default' },
              { name: '12/01/2025', value: 150, status: 'default' },
              { name: '13/01/2025', value: 150, status: 'default' },
              { name: '14/01/2025', value: 150, status: 'default' },
              { name: '15/01/2025', value: 150, status: 'default' },
              { name: '16/01/2025', value: 150, status: 'default' },
              { name: '17/01/2025', value: 150, status: 'default' },
              { name: '18/01/2025', value: 150, status: 'default' },
              { name: '19/01/2025', value: 150, status: 'default' },
              { name: '20/01/2025', value: 150, status: 'default' },
              { name: '21/01/2025', value: 150, status: 'default' }
            ]
          },
          {
            id: '5',
            metricName: 'Request',
            storeInfos: [
              { name: '01/01/2025', value: 'PT98203495', status: 'default' },
              { name: '02/01/2025', value: 'PT98203495', status: 'error' },
              { name: '03/01/2025', value: 'PT98203495', status: 'default' },
              { name: '04/01/2025', value: 'PT98203495', status: 'default' },
              { name: '05/01/2025', value: 'PT98203495', status: 'default' },
              { name: '06/01/2025', value: 'PT98203495', status: 'default' },
              { name: '07/01/2025', value: 'PT98203495', status: 'default' },
              { name: '08/01/2025', value: 'PT98203495', status: 'default' },
              { name: '09/01/2025', value: 'PT98203495', status: 'default' },
              { name: '10/01/2025', value: 'PT98203495', status: 'default' },
              { name: '11/01/2025', value: 'PT98203495', status: 'default' },
              { name: '12/01/2025', value: 'PT98203495', status: 'default' },
              { name: '13/01/2025', value: 'PT98203495', status: 'default' },
              { name: '14/01/2025', value: 'PT98203495', status: 'default' },
              { name: '15/01/2025', value: 'PT98203495', status: 'default' },
              { name: '16/01/2025', value: 'PT98203495', status: 'default' },
              { name: '17/01/2025', value: 'PT98203495', status: 'default' },
              { name: '18/01/2025', value: 'PT98203495', status: 'default' },
              { name: '19/01/2025', value: 'PT98203495', status: 'default' },
              { name: '20/01/2025', value: 'PT98203495', status: 'default' },
              { name: '21/01/2025', value: 'PT98203495', status: 'default' }
            ]
          }
        ]
      })
    )
  }
}


export default new MockSkuDetailRepository()
