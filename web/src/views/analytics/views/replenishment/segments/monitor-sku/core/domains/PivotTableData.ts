import { z } from 'zod'

// Define enum cho status
const StatusSchema = z.enum(['default', 'warning', 'error', 'normal'])

// StoreInfo class với methods
export class StoreInfo {
  constructor(
    public name: string,
    public value: string | number | boolean,
    public status: z.infer<typeof StatusSchema>,
  ) {}
}

// TableDataItem class với methods
export class TableDataItem {
  constructor(
    public id: string,
    public metricName: string,
    public storeInfos: StoreInfo[],
  ) {}
}

// Main SkuDetail class với methods
export class PivotTableData {
  constructor(
    public dayAxis: string[],
    public tableData: TableDataItem[],
  ) {}
}

// Define schemas với transform
const StoreInfoSchema = z
  .object({
    name: z.string(),
    value: z.union([z.string(), z.number(), z.boolean()]),
    status: StatusSchema,
  })
  .transform((data) => new StoreInfo(data.name, data.value, data.status))

const TableDataItemSchema = z
  .object({
    id: z.string(),
    metricName: z.string(),
    storeInfos: z.array(StoreInfoSchema),
  })
  .transform((data) => new TableDataItem(data.id, data.metricName, data.storeInfos))

const SkuDetailSchema = z
  .object({
    dayAxis: z.array(z.string()),
    tableData: z.array(TableDataItemSchema),
  })
  .transform((data) => new PivotTableData(data.dayAxis, data.tableData))

// Export types và schemas
export type Status = z.infer<typeof StatusSchema>
export { SkuDetailSchema, StoreInfoSchema, TableDataItemSchema, StatusSchema }

// Helper function để parse từ unknown data
export function parseSkuDetail(data: unknown): PivotTableData {
  return SkuDetailSchema.parse(data)
}
