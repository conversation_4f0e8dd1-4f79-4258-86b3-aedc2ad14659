<script setup lang="ts">
import { computed, inject } from 'vue'
import type { useSkuForecastStore } from '../../../../stores/SkuForecastStore'
import { DateRange } from '../../../../stores/DateRangeCalculator'
import ForecastChart from './ForecastChart.vue'
import ForecastSkuDetailHeader
  from '@/views/analytics/views/replenishment/segments/monitor-sku/components/body/monitor-sku-detail/forecast-chart/ForecastSkuDetailHeader.vue'

// Props
interface Props {
  productId: string
  storeId?: string
  dateRange?: DateRange
}

const props = withDefaults(defineProps<Props>(), {
  storeId: 'all',
  dateRange: DateRange.LAST_7_DAYS,
})

// Inject store từ parent component
const skuForecastStore = inject<ReturnType<typeof useSkuForecastStore>>('skuForecastStore')

if (!skuForecastStore) {
  throw new Error('skuForecastStore not provided')
}

// Computed cho forecast data hiện tại
const currentForecastData = computed(() => {
  return skuForecastStore.getCachedData(props.productId, props.storeId, props.dateRange)
})

const isLoading = computed(() => {
  return skuForecastStore.isLoadingFor(props.productId, props.storeId, props.dateRange)
})

const errorMessage = computed(() => {
  return skuForecastStore.getErrorFor(props.productId, props.storeId, props.dateRange)
})

const handleClearError = () => {
  if (props.productId && props.storeId) {
    skuForecastStore.clearError(props.productId, props.storeId, props.dateRange)
  }
}
</script>

<template>
  <AFlex vertical gap="small">
    <ForecastSkuDetailHeader/>

    <ASpin :spinning="isLoading">
      <!-- Hiển thị error message nếu có -->
      <AAlert
        v-if="errorMessage"
        type="error"
        :message="errorMessage"
        show-icon
        closable
        @close="handleClearError"
        style="margin-bottom: 16px"
      />

      <!-- Hiển thị empty state khi không có data -->
      <AEmpty
        v-else-if="!isLoading && !currentForecastData"
        description="Không có dữ liệu forecast"
      />

      <!-- Hiển thị chart khi có data -->
      <ForecastChart
        class="forecast-chart"
        v-else-if="currentForecastData"
        :forecast-data="currentForecastData"
      />
    </ASpin>
  </AFlex>
</template>

<style scoped></style>

<style>
.forecast-chart {
  box-shadow: 0 0 3px 0 rgba(16, 24, 40, 0.15);
  border-radius: 8px;
  height: 500px;
}
</style>
