import {
  PivotTableData,
  type Status,
  StoreInfo, TableDataItem
} from '@/views/analytics/views/replenishment/segments/monitor-sku/core/domains/PivotTableData.ts'
import type {
  DailyInventorySoldRow,
  GetDailyInventorySoldResponse
} from '@/views/analytics/views/replenishment/domains/responses/GetSalesResponse.ts'

export class DailyInventorySoldResponseAdapter {
  toPivotTableData(response: GetDailyInventorySoldResponse): PivotTableData {
    const rows = (response.rows as unknown) as DailyInventorySoldRow[]

    const dayAxis = this.toDayAxis(rows)
    const tableData = this.toTableData(rows)

    return new PivotTableData(dayAxis, tableData)
  }

  private toDayAxis(rows: DailyInventorySoldRow[]): string[] {
    const dates = rows.map(row => row.date).sort()
    return [...new Set(dates)]
  }

  private toTableData(rows: DailyInventorySoldRow[]): TableDataItem[] {
    const metrics = [
      {
        id: '1',
        metricName: 'Inventory quantity',
        getValue: (row: DailyInventorySoldRow) => row.inventory || 0,
        getStatus: (row: DailyInventorySoldRow): Status => this.getInventoryStatus(row.inventory || 0)
      },
      {
        id: '2',
        metricName: 'DOH',
        getValue: (row: DailyInventorySoldRow) => row.doh || 0,
        getStatus: (row: DailyInventorySoldRow): Status => this.getDOHStatus(row.doh || 0)
      },
      {
        id: '3',
        metricName: 'Sức bán thực tế',
        getValue: (row: DailyInventorySoldRow) => row.sold || 0,
        getStatus: (row: DailyInventorySoldRow): Status => this.getSoldStatus(row.sold || 0)
      },
      {
        id: '4',
        metricName: 'Sức bán dự đoán',
        getValue: (row: DailyInventorySoldRow) => row.forecastSold || 0,
        getStatus: (row: DailyInventorySoldRow): Status => this.getForecastStatus(row.forecastSold || 0)
      },
      {
        id: '5',
        metricName: 'Request',
        getValue: (row: DailyInventorySoldRow) => row.request || 0,
        getStatus: (row: DailyInventorySoldRow): Status => this.getRequestStatus(row.request || 0)
      }
    ]

    return metrics.map(metric => {
      const storeInfos = rows.map(row => new StoreInfo(
        row.date,
        metric.getValue(row),
        metric.getStatus(row)
      ))

      return new TableDataItem(metric.id, metric.metricName, storeInfos)
    })
  }

  private getInventoryStatus(inventory: number): Status {
    if (inventory <= 0) return 'error'
    if (inventory < 50) return 'warning'
    return 'normal'
  }

  private getDOHStatus(doh: number): Status {
    if (doh <= 0) return 'error'
    if (doh < 7) return 'warning'
    if (doh > 30) return 'warning'
    return 'normal'
  }

  private getSoldStatus(sold: number): Status {
    if (sold < 0) return 'error'
    if (sold === 0) return 'warning'
    return 'normal'
  }

  private getForecastStatus(forecast: number): Status {
    if (forecast < 0) return 'error'
    if (forecast === 0) return 'warning'
    return 'normal'
  }

  private getRequestStatus(request: number): Status {
    if (request < 0) return 'error'
    return 'normal'
  }
}
