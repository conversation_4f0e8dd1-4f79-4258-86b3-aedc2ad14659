
<script setup lang="ts">
import { ref, h } from 'vue'
import type { SKUModel } from '@/views/analytics/views/replenishment/segments/monitor-sku/components/body/models/SKU.model'
import MonitorSkuDetail from '@/views/analytics/views/replenishment/segments/monitor-sku/components/body/monitor-sku-detail/MonitorSkuDetail.vue'
import ProductNameCell from '@/views/analytics/views/replenishment/segments/monitor-sku/components/body/ProductNameCell.vue'
import { useAvailableWidthProvider } from '@/hooks/userContainerWidth.ts'

const props = defineProps<{
  records: SKUModel[]
  loading?: boolean
  error?: string | null
}>()

const expandedRows = ref<string[]>([])
const tableRef = ref<HTMLDivElement | null>(null)
const containerRef = ref<HTMLDivElement>()

// Sử dụng hook để provide available width cho child components
const { updateAvailableWidth } = useAvailableWidthProvider(containerRef, {
  left: 32, // margin-left của .main-table-detail
  padding: 32
})

const columns = [
  {
    title: '#',
    dataIndex: 'id',
    key: 'id',
    width: 50,
  },
  {
    title: 'Product name',
    dataIndex: 'productName',
    key: 'productName',
    width: 200,
    ellipsis: true,
    customRender: ({ text, record }: { text: string; record: SKUModel }) => {
      const isExpanded = expandedRows.value.includes(record.id)
      return h(ProductNameCell, {
        productName: text,
        isExpanded: isExpanded,
      })
    },
  },
  {
    title: 'Inventory quantity',
    children: [
      {
        title: 'DC',
        dataIndex: 'inventoryDC',
        key: 'inventoryDC',
        width: 100,
      },
      {
        title: 'Store',
        dataIndex: 'inventoryStore',
        key: 'inventoryStore',
        width: 100,
      },
    ],
  },
  {
    title: 'Forecast',
    children: [
      {
        title: 'Week',
        dataIndex: 'forecastWeek',
        key: 'forecastWeek',
        width: 100,
      },
      {
        title: '1 Month',
        dataIndex: 'forecastMonth',
        key: 'forecastMonth',
        width: 100,
      },
      {
        title: '3 Month',
        dataIndex: 'forecast3Month',
        key: 'forecast3Month',
        width: 100,
      },
      {
        title: '6 Month',
        dataIndex: 'forecast6Month',
        key: 'forecast6Month',
        width: 100,
      },
      {
        title: '1 Year',
        dataIndex: 'forecastYear',
        key: 'forecastYear',
        width: 100,
      },
    ],
  },
]

const handleExpandRow = (expanded: boolean, record: SKUModel) => {
  if (expanded) {
    expandedRows.value.push(record.id)
  } else {
    expandedRows.value = expandedRows.value.filter((id) => id !== record.id)
  }

  // Update available width khi expand/collapse vì có thể ảnh hưởng đến layout
  setTimeout(() => {
    updateAvailableWidth()
  }, 100)
}
</script>

<template>
  <div ref="containerRef" class="main-table-container">
    <!-- Error Display -->
    <AAlert
      v-if="props.error"
      :message="props.error"
      type="error"
      show-icon
      closable
      style="margin-bottom: 16px"
    />

    <!-- Table -->
    <ATable
      ref="tableRef"
      :columns="columns"
      :scroll="{ x: 'max-content' }"
      :data-source="props.records"
      :pagination="false"
      :expandIcon="() => null"
      expandRowByClick
      @expand="handleExpandRow"
      :loading="props.loading"
      row-key="id"
      :show-expand-column="false"
      class="main-table"
    >
      <template #expandedRowRender="{ record }">
        <MonitorSkuDetail class="main-table-detail" :product-id="record.productName" />
      </template>
    </ATable>
  </div>
</template>

<style scoped src="/src/assets/pivot-tables.css"></style>
