<script setup lang="ts">
import { toRef } from 'vue'
import type { PivotTableData } from '@/views/analytics/views/replenishment/segments/monitor-sku/core/domains/PivotTableData.ts'
import { usePivotTable } from '@/hooks/usePivotTable.ts'

interface Props {
  skuDetail: PivotTableData
}

const props = defineProps<Props>()

// Sử dụng hook pivot table với SKU detail specific options
const {
  tableRef,
  availableWidth,
  columns,
  dataSource,
  scrollConfig
} = usePivotTable(toRef(props, 'skuDetail'), {
  statusClassPrefix: 'status',
  metricColumnWidth: 150,
  dataColumnWidth: 120
})
</script>

<template>
  <div
    ref="tableRef"
    class="pivot-table-container table-hover-effects"
    :style="{ maxWidth: `${availableWidth}px` }"
  >
    <ATable
      :columns="columns"
      :data-source="dataSource"
      :scroll="scrollConfig"
      :pagination="false"
      row-key="id"
      class="pivot-table"
    />
  </div>
</template>

<style scoped src="/src/assets/pivot-tables.css"></style>
<style lang="css" src="/src/assets/table-hover.css"></style>
