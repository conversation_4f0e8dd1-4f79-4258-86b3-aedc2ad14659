
import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import type { SKUModel } from '@/views/analytics/views/replenishment/segments/monitor-sku/components/body/models/SKU.model'
import { DateRange, getDateRangeTimestamp } from './DateRangeCalculator.ts'
import skuService from '@/views/analytics/views/replenishment/segments/monitor-sku/core/services/SkuService'
import type { GetSkuListingRequest } from '@/views/analytics/views/replenishment/domains'

export const useSKUStore = defineStore('sku', () => {
  // State
  const skuList = ref<SKUModel[]>([])
  const loading = ref(false)
  const error = ref<string | null>(null)
  const currentDateRange = ref<DateRange>(DateRange.LAST_7_DAYS)

  // Cache - simple key-value map
  const cache = new Map<DateRange, SKUModel[]>()

  // Computed
  const hasError = computed(() => error.value !== null)
  const isEmpty = computed(() => skuList.value.length === 0 && !loading.value && !hasError.value)

  // Actions
  const fetchSkuList = async (dateRange: DateRange = currentDateRange.value) => {
    // Check cache first
    if (cache.has(dateRange)) {
      skuList.value = cache.get(dateRange)!
      currentDateRange.value = dateRange
      error.value = null
      return
    }

    loading.value = true
    error.value = null

    try {
      const { startDate, endDate } = getDateRangeTimestamp(dateRange)
      const request: GetSkuListingRequest = {
        startDate,
        endDate
      }

      const data = await skuService.getSkuListing(request)
      // Cache the result
      cache.set(dateRange, data)

      skuList.value = data
      currentDateRange.value = dateRange
    } catch (err) {
      error.value = err instanceof Error ? err.message : 'Có lỗi xảy ra khi tải dữ liệu SKU'
      skuList.value = []
    } finally {
      loading.value = false
    }
  }

  const clearCache = () => {
    cache.clear()
  }

  const clearError = () => {
    error.value = null
  }

  const refreshCurrentRange = async () => {
    // Remove from cache and fetch again
    cache.delete(currentDateRange.value)
    await fetchSkuList(currentDateRange.value)
  }

  return {
    // State
    skuList,
    loading,
    error,
    currentDateRange,

    // Computed
    hasError,
    isEmpty,

    // Actions
    fetchSkuList,
    clearCache,
    clearError,
    refreshCurrentRange
  }
})
