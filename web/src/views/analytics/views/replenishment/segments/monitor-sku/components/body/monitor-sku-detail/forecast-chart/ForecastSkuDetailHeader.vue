<script setup lang="ts">

import { ref } from 'vue'
import type { SelectProps } from 'ant-design-vue'

const algorithm = ref<string | undefined>('XGBoost')

const algorithmOptions = ref<SelectProps['options']>([
  { value: 'XGBoost', label: 'XGBoost' },
  { value: 'XGBoost_2', label: 'XGBoost 2' },
  { value: 'XGBoost_3', label: 'XGBoost 3' },
]);

const filterOption = (input: string, option: any) => {
  return option.value.toLowerCase().indexOf(input.toLowerCase()) >= 0;
};


const store = ref<string | undefined>(undefined);

const storeOptions = ref<SelectProps['options']>([
  { value: 'store_1', label: 'KFM_HCM_Q01 - 31 Tân Mỹ - MART' },
  { value: 'store_2', label: 'KFM_HCM_Q02 - 31 Tân Mỹ - MART' },
  { value: 'store_3', label: 'KFM_HCM_Q03 - 31 Tân Mỹ - MART' },
])
</script>

<template>
  <AFlex align="center" justify="space-between">
    <AFlex gap="small" align="center">
      <ATypographyTitle :level="5">Forecast Algorithm</ATypographyTitle>
      <ASelect
        v-model:value="algorithm"
        show-search
        placeholder="Select an algorithm"
        style="width: 200px"
        :options="algorithmOptions"
        :filter-option="filterOption"
      />
    </AFlex>

    <ASelect
      v-model:value="store"
      show-search
      placeholder="Select a store"
      style="width: 400px"
      :options="storeOptions"
      :filter-option="filterOption"
    />
  </AFlex>
</template>
