import type { PivotTableData } from '@/views/analytics/views/replenishment/segments/monitor-sku/core/domains/PivotTableData.ts'
import repository, {
  type SkuDetailRepository,
} from '@/views/analytics/views/replenishment/segments/monitor-sku/core/repositories/SkuDetailRepository.ts'
import queryRepository, {
  QueryRepository,
} from '@/views/analytics/views/replenishment/repositories/QueryRepository.ts'
import type { GetDailyInventorySoldRequest, GetSalesRequest } from '@/views/analytics/views/replenishment/domains'
import type {
  ForecastData,
  GetSalesResponse,
  GetDayOnHandResponse
} from '@/views/analytics/views/replenishment/domains/responses/GetSalesResponse.ts'
import {
  SalesResponseAdapter
} from '@/views/analytics/views/replenishment/segments/monitor-sku/core/helpers/SalesResponseAdapter.ts'
import {
  DayOnHandResponseAdapter
} from '@/views/analytics/views/replenishment/segments/monitor-sku/core/helpers/DayOnHandResponseAdapter.ts'
import type { GetDayOnHandRequest } from '@/views/analytics/views/replenishment/domains'
import {
  DailyInventorySoldResponseAdapter
} from '@/views/analytics/views/replenishment/segments/monitor-sku/core/helpers/DailyInventorySoldResponseAdapter.ts'

export abstract class SkuDetailService {
  abstract getSkuDetailByProductId(request: GetSalesRequest): Promise<PivotTableData>

  abstract getForecastDetail(request: GetSalesRequest): Promise<ForecastData>

  abstract getMetricDetail(request: GetSalesRequest): Promise<PivotTableData>
}

export class SkuDetailServiceImpl extends SkuDetailService {
  private readonly skuDetailRepository: SkuDetailRepository
  private readonly queryRepository: QueryRepository
  private readonly dayOnHandAdapter: DayOnHandResponseAdapter
  private readonly salesAdapter: SalesResponseAdapter
  private readonly dailyInventorySoldAdapter: DailyInventorySoldResponseAdapter

  constructor(skuDetailRepository: SkuDetailRepository, queryRepository: QueryRepository) {
    super()
    this.skuDetailRepository = skuDetailRepository
    this.queryRepository = queryRepository
    this.dayOnHandAdapter = new DayOnHandResponseAdapter()
    this.salesAdapter = new SalesResponseAdapter()
    this.dailyInventorySoldAdapter = new DailyInventorySoldResponseAdapter()
  }

  async getMetricDetail(request: GetDailyInventorySoldRequest): Promise<PivotTableData> {
    const response = await this.queryRepository.getDailyInventorySold(request)
    return this.dailyInventorySoldAdapter.toPivotTableData(response);
  }

  async getSkuDetailByProductId(request: GetDayOnHandRequest): Promise<PivotTableData> {
    const response: GetDayOnHandResponse = await this.queryRepository.getDayOnHand(request)
    return this.dayOnHandAdapter.toPivotTableData(response)
  }

  async getForecastDetail(request: GetSalesRequest): Promise<ForecastData> {
    const sales: GetSalesResponse = await this.queryRepository.getSales(request)
    return this.salesAdapter.toForecastData(sales)
  }
}

export default new SkuDetailServiceImpl(repository, queryRepository)
