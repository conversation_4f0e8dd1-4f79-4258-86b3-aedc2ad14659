
<script setup lang="ts">
import { RightOutlined } from '@ant-design/icons-vue'

interface Props {
  productName: string
  isExpanded: boolean
}

defineProps<Props>()
</script>

<template>
  <div class="product-name-cell">
    <RightOutlined :class="['expand-icon', { 'is-expanded': isExpanded }]" />
    <span class="product-name">{{ productName }}</span>
  </div>
</template>

<style scoped>
.product-name-cell {
  display: flex;
  align-items: center;
  gap: 8px;
}

.expand-icon {
  transition: transform 0.3s ease;
}

.expand-icon.is-expanded {
  transform: rotate(90deg);
}

.product-name {
  margin-left: 4px;
}
</style>
