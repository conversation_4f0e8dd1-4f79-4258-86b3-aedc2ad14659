import type { SkuListingResponse } from '@/views/analytics/views/replenishment/domains/responses/GetSalesResponse.ts'
import type { SKUModel } from '@/views/analytics/views/replenishment/segments/monitor-sku/components/body/models/SKU.model.ts'

export class SkuListingResponseAdapter {
  static adapt(response: SkuListingResponse): SKUModel[] {
    if (!response || !Array.isArray(response.rows)) {
      return []
    }

    return response.rows.map((item, index) => ({
      id: `${index + 1}`,
      productName: item.sku || '',
      inventoryDC: item.total_latest_inventory || 0,
      inventoryStore: 0,
      forecastWeek: 0,
      forecastMonth: 0,
      forecast3Month: 0,
      forecast6Month: 0,
      forecastYear: 0,
    }))
  }
}
