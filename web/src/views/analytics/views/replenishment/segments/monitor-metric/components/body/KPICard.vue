<script setup lang="ts">
import { ScheduleOutlined } from '@ant-design/icons-vue'

defineProps<{
  title: string
  value: string
  color: string
}>()
</script>

<template>
  <ACard size="default" class="kpi-container"  :style="{ '--card-color': color }"
  >
      <AFlex align="baseline" gap="middle">
        <slot name="icon"/>
        <ScheduleOutlined :style="{ fontSize: '14px', color: color }" />
        <AFlex vertical>
          <ATypography>{{ title }}</ATypography>
          <ATypographyTitle :level="3">{{ value }}</ATypographyTitle>
        </AFlex>
      </AFlex>
  </ACard>
</template>

<style>
.kpi-container{
  flex: 1
}

.kpi-container .ant-card-body{
  padding: 1rem 0.75rem;
}

.kpi-container::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background-color: var(--card-color);
  border-radius: 12px 12px 0 0;
}
.kpi-container h3{
  margin: 0 !important;
}
</style>
