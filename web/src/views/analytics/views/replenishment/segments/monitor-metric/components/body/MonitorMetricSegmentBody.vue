<script setup lang="ts">

import KPICard from '@/views/analytics/views/replenishment/segments/monitor-metric/components/body/KPICard.vue'
import { ScheduleOutlined } from '@ant-design/icons-vue'
</script>

<template>
  <AFlex align="center" gap="middle">
    <KPICard title="Tổng số giờ hết hàng" value="0" color="orange">
      <ScheduleOutlined :style="{ fontSize: '14px' }" />
    </KPICard>
    <KPICard title="Tổng tiền lost sales" value="0" color="blue">
      <ScheduleOutlined :style="{ fontSize: '14px' }"/>
    </KPICard>
    <KPICard title="Forecast performance" value="0" color="cyan">
      <ScheduleOutlined :style="{ fontSize: '14px' }"/>
    </KPICard>
  </AFlex>
</template>

<style scoped>

</style>
