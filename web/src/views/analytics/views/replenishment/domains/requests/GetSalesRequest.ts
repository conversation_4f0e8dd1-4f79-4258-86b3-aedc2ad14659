export type TimeStampAsSeconds = number

export type GetSalesRequest = {
  storeId: string
  startDate: TimeStampAsSeconds
  endDate: TimeStampAsSeconds
  sku: string
}

export type GetDayOnHandRequest = {
  startDate: TimeStampAsSeconds
  endDate: TimeStampAsSeconds
  sku: string
}

export type GetSkuListingRequest = {
  startDate: TimeStampAsSeconds
  endDate: TimeStampAsSeconds
}

export type GetDailyInventorySoldSample = {
  name: 'get_daily_inventory_sold'
  storeId: 'store_001'
  sku: 'Smart Watch Elite'
  startDate: 1704096000
  endDate: 1704201600
}

export type GetDailyInventorySoldRequest = {
  storeId: string
  sku: string
  startDate: TimeStampAsSeconds
  endDate: TimeStampAsSeconds
}
