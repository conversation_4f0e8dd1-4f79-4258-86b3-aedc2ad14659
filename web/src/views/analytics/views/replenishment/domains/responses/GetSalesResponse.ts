import type { QueryResponse } from '@/views/analytics/views/replenishment/domains/responses/QueryResponse.ts'

export type SaleRow = {
  totalQuantitySold: number;
  totalOpeningInventory: number;
  voucherProgram: string;
  weatherCondition: string;
  saleDate: string;
  storeId: string;
  isWeekend: number;
}

export type DayOnHandRow = {
  currentInventory: number;
  storeId: string;
  daysOnHand: number;
}

export type SkuRow = {
  sku: string;
  total_latest_inventory: number;
}

export type ForecastCategory = {
  date: string;
  isWeekend: number;
  weatherCondition: string;
  forecast: boolean
}

export type SeriesDataItem = {
  quantity: number;
  skuPerDay: number;
}

export type ForecastData = {
  categories: ForecastCategory[];
  saleVolumes: number[]
  saleVolumesPredict: number[]
  voucherPrograms: string[]
  seriesData: SeriesDataItem[]
}

export type DailyInventorySoldRow = {
  date: string
  sold: number
  inventory: number
  doh?: number
  forecastSold?: number
  request?: number
}


export type GetSalesResponse = QueryResponse<SaleRow>;

export type GetDayOnHandResponse = QueryResponse<DayOnHandRow>;

export type SkuListingResponse = QueryResponse<SkuRow>;


export type GetDailyInventorySoldResponse = QueryResponse<DailyInventorySoldRow>;
