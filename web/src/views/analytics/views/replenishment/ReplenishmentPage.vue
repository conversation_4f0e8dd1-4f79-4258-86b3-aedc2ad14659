<script setup lang="ts">
import MonitorMetricSegment from '@/views/analytics/views/replenishment/segments/monitor-metric/MonitorMetricSegment.vue'
import MonitorSkuSegment from '@/views/analytics/views/replenishment/segments/monitor-sku/MonitorSkuSegment.vue'
import { DateRange } from '@/views/analytics/views/replenishment/segments/monitor-sku/stores/DateRangeCalculator.ts'
import { ref } from 'vue'

const dateRange = ref<DateRange>(DateRange.LAST_7_DAYS)

</script>

<template>
  <AFlex vertical gap="middle">
    <MonitorMetricSegment v-model:date-range="dateRange" />
    <MonitorSkuSegment :dateRange="dateRange"/>
  </AFlex>
</template>
