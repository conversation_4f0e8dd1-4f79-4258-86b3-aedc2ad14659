<script setup lang="ts">
import { useRoute, useRouter } from 'vue-router'
import { computed } from 'vue'
import SidebarAction from '@/views/analytics/components/SidebarAction.vue'
import Appbar from '@/views/analytics/components/Appbar.vue'
import Sidebar from '@/views/analytics/components/Sidebar.vue'
import { useSidebarStore } from '@/stores/sidebarStore'

const router = useRouter()
const route = useRoute()
const sidebarStore = useSidebarStore()

const activeTab = computed(() => {
  return route.name?.toString()?.toLowerCase() ?? ''
})

const collapsed = computed(() => sidebarStore.isCollapsed)

const tabs = computed(() => {
  return [
    {
      key: 'inventory-overview',
      title: 'Inventory Overview',
    },
    {
      key: 'pt-calendar',
      title: 'PT Calendar',
    },
    {
      key: 'po-calendar',
      title: 'PO Calendar',
    },
    {
      key: 'inventory-detail',
      title: 'Inventory Detail',
    },
    {
      key: 'replenishment',
      title: 'Replenishment',
    },
    {
      key: 'performance-report',
      title: 'Performance Report',
    },
  ]
})

const onTabChange = (key: string) => {
  router.push({ name: key.charAt(0).toUpperCase() + key.slice(1) }) // tab1 -> Tab1
}
</script>

<template>
  <ALayout>
    <Appbar />
    <ALayout :style="{background: `white`, marginTop: `48px`}">
      <AFlex style="flex: 1; height: calc(100vh - 48px);">
        <Sidebar />
        <AFlex
          vertical
          :style="{
            flex: 1,
            marginLeft: collapsed ? '0px' : '320px',
            transition: 'margin-left 0.3s ease',
            height: '100%',
            overflow: 'hidden'
          }"
          class="main-content"
        >
          <SidebarAction />
          <ATabs :activeKey="activeTab" @change="onTabChange" size="middle" class="tabs-container">
            <ATabPane v-for="tab in tabs" :key="tab.key">
              <template #tab>
                <span>{{ tab.title }}</span>
              </template>
            </ATabPane>
          </ATabs>
          <div class="tab-container">
            <router-view />
          </div>
        </AFlex>
      </AFlex>
    </ALayout>
  </ALayout>
</template>

<style>
.main-content {
  height: calc(100vh - 48px);
  overflow: hidden;
}

.tab-container {
  padding: 1rem 1.5rem;
  flex: 1;
  overflow-y: auto; /* Cho phép content scroll độc lập */
  height: 0; /* Trick để flex item có thể scroll */
}

.tabs-container .ant-tabs-nav-list {
  padding: 0 1rem;
}

/* Tùy chỉnh scrollbar cho content */
.tab-container::-webkit-scrollbar {
  width: 8px;
}

.tab-container::-webkit-scrollbar-track {
  background: #f1f1f1;
}

.tab-container::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 4px;
}

.tab-container::-webkit-scrollbar-thumb:hover {
  background: #a1a1a1;
}
</style>
