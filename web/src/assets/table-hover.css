/* Table Hover Effects - Reusable CSS for all tables */
.table-hover-effects {
  --table-hover-header-bg: #D1E0FF;
  --table-hover-cell-bg: #F9FAFB;
  --table-hover-border-color: #155EEF;
  --table-hover-transition: all 0.3s ease;
  --border-width: 0.5px;
  --table-border-bottom-color: #EAECF0;
}

/* Base styling - All cells have transparent border to prevent layout shift */
.table-hover-effects .ant-table-thead th,
.table-hover-effects .ant-table-tbody td {
  /*border: var(--border-width) solid transparent !important;*/
  /*transition: var(--table-hover-transition);*/
}

/* Border bottom for normal rows (not last row) */
.table-hover-effects .ant-table-tbody tr:not(:last-child) td {
  border-bottom: 1px solid var(--table-border-bottom-color) !important;
}

/* Dynamic hover effects - works with unlimited columns including grouped headers */
.table-hover-effects .ant-table-thead th.column-hovered {
  background-color: var(--table-hover-header-bg) !important;
}

.table-hover-effects .ant-table-tbody td.column-hovered {
  background-color: var(--table-hover-cell-bg) !important;
}

.table-hover-effects .ant-table-tbody td.column-hovered:hover {
  border: var(--border-width) solid var(--table-hover-border-color) !important;
}
