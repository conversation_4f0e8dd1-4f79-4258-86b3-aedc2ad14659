import { computed, h, inject, ref, type Ref } from 'vue'
import type { PivotTableData } from '@/views/analytics/views/replenishment/segments/monitor-sku/core/domains/PivotTableData.ts'
import { useTableHoverEffects } from '@/hooks/useTableHoverEffects.ts'

interface PivotTableColumn {
  title: string
  dataIndex: string
  key: string
  fixed?: 'left' | 'right'
  width: number
  customRender?: (params: { record: any }) => any
}

interface UsePivotTableOptions {
  /**
   * CSS class prefix cho status styling
   * @default 'status'
   */
  statusClassPrefix?: string
  /**
   * Width cho cột đầu tiên (#)
   * @default 50
   */
  indexColumnWidth?: number
  /**
   * Width cho cột metric
   * @default 150
   */
  metricColumnWidth?: number
  /**
   * Width cho các cột data
   * @default 120
   */
  dataColumnWidth?: number
}

export function usePivotTable(
  pivotData: Ref<PivotTableData | undefined>,
  options: UsePivotTableOptions = {}
) {
  const {
    statusClassPrefix = 'status',
    indexColumnWidth = 50,
    metricColumnWidth = 150,
    dataColumnWidth = 120
  } = options

  const tableRef = ref<HTMLDivElement>()
  const availableWidth = inject<Ref<number>>('availableWidth', ref(800))

  useTableHoverEffects(tableRef)

  // Generate columns từ pivot data
  const columns = computed((): PivotTableColumn[] => {
    if (!pivotData.value || !pivotData.value.tableData.length) {
      return []
    }

    const { tableData, dayAxis } = pivotData.value

    // Lấy tên các cột data (stores hoặc dates)
    const dataColumns = dayAxis || [
      ...new Set(tableData.flatMap((item) => item.storeInfos.map((store) => store.name))),
    ]

    return [
      {
        title: '#',
        dataIndex: 'id',
        key: 'id',
        fixed: 'left',
        width: indexColumnWidth,
      },
      {
        title: 'Metric',
        dataIndex: 'metricName',
        key: 'metricName',
        fixed: 'left',
        width: metricColumnWidth,
      },
      ...dataColumns.map((columnName) => ({
        title: columnName,
        dataIndex: columnName.toLowerCase().replace(/\s+/g, '_'),
        key: columnName.toLowerCase().replace(/\s+/g, '_'),
        width: dataColumnWidth,
        customRender: ({ record }: { record: any }) => {
          const storeInfo = record.storeInfos.find((info: any) => info.name === columnName)
          if (!storeInfo) return h('div', {}, '-')

          const statusClass = `${statusClassPrefix}-${storeInfo.status}`
          return h('div', { class: statusClass }, storeInfo.value)
        },
      })),
    ]
  })

  // Generate data source từ pivot data
  const dataSource = computed(() => {
    if (!pivotData.value || !pivotData.value.tableData.length) {
      return []
    }

    const { tableData } = pivotData.value
    return tableData.map((item) => ({
      id: item.id,
      metricName: item.metricName,
      ...Object.fromEntries(
        item.storeInfos.map((store) => [
          store.name.toLowerCase().replace(/\s+/g, '_'),
          store
        ]),
      ),
      storeInfos: item.storeInfos,
    }))
  })

  // Tính tổng width
  const totalWidth = computed(() => {
    return columns.value.reduce((sum, col) => sum + (col.width || 0), 0)
  })

  // Scroll config
  const scrollConfig = computed(() => ({
    x: totalWidth.value
  }))

  return {
    tableRef,
    availableWidth,
    columns,
    dataSource,
    totalWidth,
    scrollConfig
  }
}
