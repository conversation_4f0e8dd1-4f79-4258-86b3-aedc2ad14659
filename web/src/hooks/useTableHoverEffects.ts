
import { ref, onMounted, onUnmounted, type Ref } from 'vue'

export const useTableHoverEffects = (tableRef: Ref<HTMLDivElement | undefined>) => {
  const hoveredColumn = ref<number | null>(null)

  const clearAllHoverClasses = (tableElement: Element) => {
    // Remove hover classes from headers
    const headers = tableElement.querySelectorAll('.ant-table-thead th')
    headers.forEach(header => header.classList.remove('column-hovered'))

    // Remove hover classes from cells
    const cells = tableElement.querySelectorAll('.ant-table-tbody td')
    cells.forEach(cell => cell.classList.remove('column-hovered'))
  }

  const addHoverClasses = (tableElement: Element, columnIndex: number) => {
    // Highlight the actual body column
    const cells = tableElement.querySelectorAll(`.ant-table-tbody td:nth-child(${columnIndex + 1})`)
    cells.forEach(cell => cell.classList.add('column-hovered'))

    // Highlight the corresponding leaf header (bottom row of headers)
    const leafHeaders = tableElement.querySelectorAll('.ant-table-thead tr:last-child th')
    const targetLeafHeader = leafHeaders[columnIndex]
    if (targetLeafHeader) {
      targetLeafHeader.classList.add('column-hovered')
    }

    // Find and highlight the parent header (top row)
    const firstRowHeaders = tableElement.querySelectorAll('.ant-table-thead tr:first-child th')
    let leafIndexCounter = 0

    firstRowHeaders.forEach((parentHeader: Element) => {
      const colspan = parseInt(parentHeader.getAttribute('colspan') || '1')

      // Check if our column index falls within this parent header's range
      if (columnIndex >= leafIndexCounter && columnIndex < leafIndexCounter + colspan) {
        parentHeader.classList.add('column-hovered')
      }

      leafIndexCounter += colspan
    })
  }

  const getColumnIndexFromCell = (cell: HTMLTableCellElement): number => {
    const row = cell.parentElement as HTMLTableRowElement
    if (!row) return -1

    // Simply get the index of the cell in its row
    const cells = Array.from(row.children) as HTMLTableCellElement[]
    return cells.indexOf(cell)
  }

  const handleMouseOver = (event: MouseEvent) => {
    const target = event.target as HTMLElement
    const cell = target.closest('td') as HTMLTableCellElement
    if (!cell) return

    const cellIndex = getColumnIndexFromCell(cell)
    if (cellIndex === -1) return

    // Only update if column changed
    if (hoveredColumn.value !== cellIndex) {
      hoveredColumn.value = cellIndex

      const tableElement = tableRef.value?.querySelector('.ant-table')
      if (tableElement) {
        clearAllHoverClasses(tableElement)
        addHoverClasses(tableElement, cellIndex)
      }
    }
  }

  const handleMouseOut = (event: MouseEvent) => {
    const relatedTarget = event.relatedTarget as HTMLElement

    // Check if we're still within the table
    const tableBody = tableRef.value?.querySelector('.ant-table-tbody')
    if (!tableBody) return

    // If we're moving to another cell in the same table, don't clear
    if (relatedTarget && tableBody.contains(relatedTarget)) {
      return
    }

    // Clear hover state when truly leaving the table
    hoveredColumn.value = null
    const tableElement = tableRef.value?.querySelector('.ant-table')
    if (tableElement) {
      clearAllHoverClasses(tableElement)
    }
  }

  onMounted(() => {
    const tableElement: any = tableRef.value?.querySelector('.ant-table-tbody')
    if (tableElement) {
      tableElement.addEventListener('mouseover', handleMouseOver)
      tableElement.addEventListener('mouseleave', handleMouseOut)
    }
  })

  onUnmounted(() => {
    const tableElement: any = tableRef.value?.querySelector('.ant-table-tbody')
    if (tableElement) {
      tableElement.removeEventListener('mouseover', handleMouseOver)
      tableElement.removeEventListener('mouseleave', handleMouseOut)
    }
  })

  return {
    hoveredColumn
  }
}
