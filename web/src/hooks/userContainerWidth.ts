import { ref, onMounted, onUnmounted, provide, nextTick, type Ref } from 'vue'

export function useContainerWidth(containerRef: Ref<HTMLElement | undefined>) {
  const containerWidth = ref(0)

  const updateWidth = () => {
    if (containerRef.value) {
      containerWidth.value = containerRef.value.offsetWidth
    }
  }

  onMounted(() => {
    nextTick(() => {
      updateWidth()
    })
    window.addEventListener('resize', updateWidth)
  })

  onUnmounted(() => {
    window.removeEventListener('resize', updateWidth)
  })

  return {
    containerWidth,
    updateWidth
  }
}

/**
 * Hook để provide available width cho child components
 * Tự động tính toán available width và provide xuống child
 */
export function useAvailableWidthProvider(
  containerRef: Ref<HTMLElement | undefined>,
  margins: { left?: number; right?: number; padding?: number } = {}
) {
  const { left = 32, right = 0, padding = 32 } = margins
  const { containerWidth, updateWidth } = useContainerWidth(containerRef)

  const availableWidth = ref(0)

  const updateAvailableWidth = () => {
    updateWidth()
    availableWidth.value = Math.max(0, containerWidth.value - left - right - padding)
  }

  onMounted(() => {
    nextTick(() => {
      updateAvailableWidth()
    })
    window.addEventListener('resize', updateAvailableWidth)
  })

  onUnmounted(() => {
    window.removeEventListener('resize', updateAvailableWidth)
  })

  // Provide cho child components
  provide('availableWidth', availableWidth)

  return {
    containerWidth,
    availableWidth,
    updateAvailableWidth
  }
}
