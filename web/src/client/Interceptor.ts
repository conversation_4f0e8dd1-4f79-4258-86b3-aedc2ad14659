import { type HttpInterceptor,type HttpRequestConfig,type HttpResponse } from './HttpClient';

// Authentication Interceptor
export const authInterceptor = (getToken: () => string | null): HttpInterceptor => ({
  request: async (config: HttpRequestConfig): Promise<HttpRequestConfig> => {
    const token = getToken();
    if (token) {
      config.headers = {
        ...config.headers,
        Authorization: `Bearer ${token}`,
      };
    }
    return config;
  },
});

// Logging Interceptor
export const loggingInterceptor: HttpInterceptor = {
  request: async (config: HttpRequestConfig): Promise<HttpRequestConfig> => {
    console.log('🚀 Request:',config);
    return config;
  },
  response: async (response: HttpResponse): Promise<HttpResponse> => {
    console.log('✅ Response:',response);
    return response;
  },
  error: async (error: any): Promise<any> => {
    console.error('❌ Error:', error);
    throw error;
  },
};

// Retry Interceptor
export const retryInterceptor = (maxRetries: number = 3, delay: number = 1000): HttpInterceptor => ({
  error: async (error: any): Promise<any> => {
    const { config } = error;

    if (!config || !config.retry) {
      config.retry = 0;
    }

    if (config.retry < maxRetries && error.response?.status >= 500) {
      config.retry++;

      await new Promise(resolve => setTimeout(resolve, delay * config.retry));

      // Retry the request
      return Promise.reject(error);
    }

    throw error;
  },
});

// Response Time Interceptor
export const responseTimeInterceptor: HttpInterceptor = {
  request: async (config: HttpRequestConfig): Promise<HttpRequestConfig> => {
    (config as any).startTime = Date.now();
    return config;
  },
  response: async (response: HttpResponse): Promise<HttpResponse> => {
    const config = (response as any).config;
    if (config?.startTime) {
      const duration = Date.now() - config.startTime;
      console.log(`⏱️ Request took ${duration}ms`);
    }
    return response;
  },
};
