
import { HttpClient, type HttpInterceptor, type HttpResponse } from './HttpClient'
import { AxiosHttpClient } from './AxiosHttpClient';
import { HttpClientType } from './HttpClientFactory';
import { authInterceptor, loggingInterceptor, retryInterceptor, responseTimeInterceptor } from './Interceptor';

export class HttpClientBuilder {
  private baseURL: string = '';
  private timeout: number = 5000;
  private type: HttpClientType = HttpClientType.AXIOS;
  private headers: Record<string, string> = {};
  private interceptors: HttpInterceptor[] = [];

  setBaseURL(baseURL: string): HttpClientBuilder {
    this.baseURL = baseURL;
    return this;
  }

  setTimeout(timeout: number): HttpClientBuilder {
    this.timeout = timeout;
    return this;
  }

  setType(type: HttpClientType): HttpClientBuilder {
    this.type = type;
    return this;
  }

  addHeader(key: string, value: string): HttpClientBuilder {
    this.headers[key] = value;
    return this;
  }

  addHeaders(headers: Record<string, string>): HttpClientBuilder {
    this.headers = { ...this.headers, ...headers };
    return this;
  }

  addInterceptor(interceptor: HttpInterceptor): HttpClientBuilder {
    this.interceptors.push(interceptor);
    return this;
  }

  addAuthInterceptor(getToken: () => string | null): HttpClientBuilder {
    this.interceptors.push(authInterceptor(getToken));
    return this;
  }

  addLoggingInterceptor(): HttpClientBuilder {
    this.interceptors.push(loggingInterceptor);
    return this;
  }

  addRetryInterceptor(maxRetries: number = 3, delay: number = 1000): HttpClientBuilder {
    this.interceptors.push(retryInterceptor(maxRetries, delay));
    return this;
  }

  addResponseTimeInterceptor(): HttpClientBuilder {
    this.interceptors.push(responseTimeInterceptor);
    return this;
  }

  build(): HttpClient {
    if (!this.baseURL) {
      throw new Error('Base URL is required');
    }

    let client: HttpClient;

    switch (this.type) {
      case HttpClientType.AXIOS:
        client = new AxiosHttpClient(this.baseURL, this.timeout);
        break;
      default:
        throw new Error(`Unsupported HTTP client type: ${this.type}`);
    }

    // Add headers
    Object.entries(this.headers).forEach(([key, value]) => {
      client.setDefaultHeader(key, value);
    });

    // Add interceptors
    this.interceptors.forEach(interceptor => {
      client.addInterceptor(interceptor);
    });

    return client;
  }

  // Static factory methods
  static create(): HttpClientBuilder {
    return new HttpClientBuilder();
  }

  static forAPI(baseURL: string): HttpClientBuilder {
    return new HttpClientBuilder().setBaseURL(baseURL);
  }
}

const client = HttpClientBuilder.create()
  .setBaseURL(import.meta.env.VITE_DOMAIN_URL as string)
  .build();

export default client;
