import { HttpClient } from './HttpClient';
import { AxiosHttpClient } from './AxiosHttpClient';

export enum HttpClientType {
  AXIOS = 'axios',
  // <PERSON><PERSON> thể thêm fetch, xhr, etc.
}

export interface HttpClientConfig {
  baseURL: string;
  timeout?: number;
  type?: HttpClientType;
}

export class HttpClientFactory {
  private static instances: Map<string, HttpClient> = new Map();

  static create(config: HttpClientConfig): HttpClient {
    const { baseURL, timeout = 5000, type = HttpClientType.AXIOS } = config;
    const key = `${type}-${baseURL}-${timeout}`;

    if (this.instances.has(key)) {
      return this.instances.get(key)!;
    }

    let client: HttpClient;

    switch (type) {
      case HttpClientType.AXIOS:
        client = new AxiosHttpClient(baseURL, timeout);
        break;
      default:
        throw new Error(`Unsupported HTTP client type: ${type}`);
    }

    this.instances.set(key, client);
    return client;
  }

  static createAxiosClient(baseURL: string, timeout?: number): AxiosHttpClient {
    return new AxiosHttpClient(baseURL, timeout);
  }

  static clearCache(): void {
    this.instances.clear();
  }

  static getFromCache(type: HttpClientType, baseURL: string, timeout: number = 5000): HttpClient | undefined {
    const key = `${type}-${baseURL}-${timeout}`;
    return this.instances.get(key);
  }
}
