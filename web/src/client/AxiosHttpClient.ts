import axios, { type AxiosInstance, type AxiosRequestConfig, type AxiosResponse } from 'axios'
import {
  HttpClient,
  type HttpRequestConfig,
  type HttpResponse,
  type HttpInterceptor,
} from './HttpClient'

export class AxiosHttpClient extends HttpClient {
  private axiosInstance: AxiosInstance

  constructor(baseURL: string, timeout: number = 5000) {
    super(baseURL, timeout)
    this.axiosInstance = axios.create({
      baseURL: this.baseURL,
      timeout: this.timeout,
      headers: this.defaultHeaders,
    })

    this.setupInterceptors()
  }

  private setupInterceptors(): void {
    // Request interceptor
    this.axiosInstance.interceptors.request.use(
      async (config) => {
        let modifiedConfig: any = { ...config }

        // Apply custom interceptors
        for (const interceptor of this.interceptors) {
          if (interceptor.request) {
            modifiedConfig = await interceptor.request(modifiedConfig)
          }
        }

        return modifiedConfig
      },
      async (error) => {
        // Apply error interceptors
        for (const interceptor of this.interceptors) {
          if (interceptor.error) {
            error = await interceptor.error(error)
          }
        }
        return Promise.reject(error)
      },
    )

    // Response interceptor
    this.axiosInstance.interceptors.response.use(
      async (response: AxiosResponse) => {
        let modifiedResponse: any = this.transformAxiosResponse(response.data)
        // Apply custom interceptors
        for (const interceptor of this.interceptors) {
          if (interceptor.response) {
            modifiedResponse = await interceptor.response(modifiedResponse)
          }
        }

        return modifiedResponse
      },
      async (error) => {
        // Apply error interceptors
        for (const interceptor of this.interceptors) {
          if (interceptor.error) {
            error = await interceptor.error(error)
          }
        }
        return Promise.reject(error)
      },
    )
  }

  private transformAxiosResponse<T>(axiosResponse: AxiosResponse<T>): HttpResponse<T> {
    return {
      data: axiosResponse.data,
      status: axiosResponse.status,
      statusText: axiosResponse.statusText,
      headers: axiosResponse.headers as Record<string, string>,
    }
  }

  private mergeConfig(config?: HttpRequestConfig): AxiosRequestConfig {
    return {
      headers: { ...this.defaultHeaders, ...config?.headers },
      timeout: config?.timeout || this.timeout,
      params: config?.params,
      data: config?.data,
    }
  }

  async get<T = any>(url: string, config?: HttpRequestConfig): Promise<HttpResponse<T>> {
    const response: AxiosResponse<T> = await this.axiosInstance.get<T>(
      url,
      this.mergeConfig(config),
    )
    return this.transformAxiosResponse(response)
  }

  async post<T = any>(
    url: string,
    data?: any,
    config?: HttpRequestConfig,
  ): Promise<HttpResponse<T>> {
    const response = await this.axiosInstance.post<T>(url, data, this.mergeConfig(config))
    return this.transformAxiosResponse<T>(response)
  }

  async put<T = any>(
    url: string,
    data?: any,
    config?: HttpRequestConfig,
  ): Promise<HttpResponse<T>> {
    const response = await this.axiosInstance.put<T>(url, data, this.mergeConfig(config))
    return this.transformAxiosResponse(response)
  }

  async patch<T = any>(
    url: string,
    data?: any,
    config?: HttpRequestConfig,
  ): Promise<HttpResponse<T>> {
    const response = await this.axiosInstance.patch<T>(url, data, this.mergeConfig(config))
    return this.transformAxiosResponse(response)
  }

  async delete<T = any>(url: string, config?: HttpRequestConfig): Promise<HttpResponse<T>> {
    const response = await this.axiosInstance.delete<T>(url, this.mergeConfig(config))
    return this.transformAxiosResponse(response)
  }

  // Override để setup lại interceptors khi có thay đổi
  addInterceptor(interceptor: HttpInterceptor): void {
    super.addInterceptor(interceptor)
    this.setupInterceptors()
  }

  removeInterceptor(interceptor: HttpInterceptor): void {
    super.removeInterceptor(interceptor)
    this.setupInterceptors()
  }
}
